// Deduction Types TypeScript definitions
// Following the patterns from category.ts but adapted for deduction-types entities

import { ApiResponse } from "./common";

// Enums matching backend schema exactly
export enum DeductionCalculationMethod {
  FIXED = 'FIXED',
  PERCENTAGE = 'PERCENTAGE',
  PROGRESSIVE = 'PROGRESSIVE',
  FORMULA = 'FORMULA',
}

export enum DeductionAppliesTo {
  GROSS = 'GROSS',
  BASIC = 'BASIC',
  NET = 'NET',
}

export enum DeductionStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING',
  EXPIRED = 'EXPIRED',
  COMPLETED = 'COMPLETED',
}

// Backend DTO: DeductionTypeDto
export interface DeductionTypeDto {
  id: string;
  businessId: string;
  deductionName: string;
  deductionCode: string;
  calculationMethod: DeductionCalculationMethod;
  amount?: number;
  isSystemDefined: boolean;
  isActive: boolean;
  appliesTo: DeductionAppliesTo;
  description?: string;
  createdBy: string;
  updatedBy?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Backend DTO: CreateDeductionTypeDto
export interface CreateDeductionTypeDto {
  deductionName: string;
  deductionCode: string;
  calculationMethod: DeductionCalculationMethod;
  amount?: number;
  isSystemDefined?: boolean;
  isActive?: boolean;
  appliesTo?: DeductionAppliesTo;
  description?: string;
}

// Backend DTO: UpdateDeductionTypeDto
export interface UpdateDeductionTypeDto {
  deductionName?: string;
  deductionCode?: string;
  calculationMethod?: DeductionCalculationMethod;
  amount?: number;
  isSystemDefined?: boolean;
  isActive?: boolean;
  appliesTo?: DeductionAppliesTo;
  description?: string;
}

// Backend DTO: DeductionTypeSlimDto
export interface DeductionTypeSlimDto {
  id: string;
  deductionName: string;
  deductionCode: string;
  calculationMethod: DeductionCalculationMethod;
  isSystemDefined: boolean;
  isActive: boolean;
  appliesTo: DeductionAppliesTo;
}

// Backend DTO: DeductionTypeListDto (optimized for table display)
export interface DeductionTypeListDto {
  id: string;
  deductionName: string;
  deductionCode: string;
  calculationMethod: DeductionCalculationMethod;
  amount?: number;
  isSystemDefined: boolean;
  isActive: boolean;
  appliesTo: DeductionAppliesTo;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Table data interface - optimized for table display
export interface DeductionTypeTableData {
  id: string;
  deductionName: string;
  deductionCode: string;
  calculationMethod: DeductionCalculationMethod;
  amount?: number;
  isSystemDefined: boolean;
  isActive: boolean;
  appliesTo: DeductionAppliesTo;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Response DTOs
export interface DeductionTypeIdResponseDto {
  id: string;
}

export interface BulkDeductionTypeIdsResponseDto {
  ids: string[];
}

export interface BulkDeleteDeductionTypeResponseDto {
  deleted: number;
  message: string;
  deletedIds: string[];
}

export interface BulkUpdateDeductionTypeStatusResponseDto {
  updated: number;
  message: string;
  updatedIds: string[];
  failed?: BulkUpdateDeductionTypeStatusFailureDto[];
}

export interface BulkUpdateDeductionTypeStatusFailureDto {
  deductionTypeId: string;
  error: string;
}

export interface PaginatedDeductionTypesResponseDto {
  data: DeductionTypeDto[];
  meta: {
    total: number;
    page: number;
    totalPages: number;
  };
}

export interface PaginatedDeductionTypesListResponseDto {
  data: DeductionTypeListDto[];
  meta: {
    total: number;
    page: number;
    totalPages: number;
  };
}

export interface DeductionTypeCodeAvailabilityResponseDto {
  available: boolean;
}

// Bulk operation DTOs
export interface BulkCreateDeductionTypeDto extends CreateDeductionTypeDto {}

export interface BulkDeleteDeductionTypeDto {
  deductionTypeIds: string[];
}

export interface BulkUpdateDeductionTypeStatusDto {
  deductionTypeIds: string[];
  isActive: boolean;
}

// API Response types
export type DeductionTypeResponse = ApiResponse<DeductionTypeDto>;
export type DeductionTypePaginatedResponse = ApiResponse<PaginatedDeductionTypesResponseDto>;
export type DeductionTypeListPaginatedResponse = ApiResponse<PaginatedDeductionTypesListResponseDto>;
export type DeductionTypeIdResponse = ApiResponse<DeductionTypeIdResponseDto>;
export type BulkDeductionTypeIdsResponse = ApiResponse<BulkDeductionTypeIdsResponseDto>;
export type BulkDeleteDeductionTypeResponse = ApiResponse<BulkDeleteDeductionTypeResponseDto>;
export type BulkUpdateDeductionTypeStatusResponse = ApiResponse<BulkUpdateDeductionTypeStatusResponseDto>;
export type DeductionTypeCodeAvailabilityResponse = ApiResponse<DeductionTypeCodeAvailabilityResponseDto>;
export type SimpleDeductionTypeResponse = ApiResponse<DeductionTypeSlimDto[]>;

// Form values interface for UI components
export interface DeductionTypeFormValues {
  deductionName: string;
  deductionCode: string;
  calculationMethod: DeductionCalculationMethod;
  amount?: number;
  isSystemDefined?: boolean;
  isActive?: boolean;
  appliesTo?: DeductionAppliesTo;
  description?: string;
}

// Legacy aliases for backward compatibility (to be removed gradually)
export type DeductionType = DeductionTypeDto;
export type SimpleDeductionTypeData = DeductionTypeSlimDto;
export type DeductionTypePaginatedData = PaginatedDeductionTypesResponseDto;

// Extended interfaces for frontend use
export interface DeductionTypeTableDataExtended extends DeductionTypeTableData {
  _id: string; // Legacy field for backward compatibility
  publicId: string; // Legacy field for backward compatibility
}
