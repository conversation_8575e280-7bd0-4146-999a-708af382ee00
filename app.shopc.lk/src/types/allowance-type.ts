import { ApiResponse } from "./common";

export enum AllowanceCalculationMethod {
  FIXED = "FIXED",
  PERCENTAGE = "PERCENTAGE",
  FORMULA = "FORMULA",
  SLAB = "SLAB",
}

export enum AllowanceStatus {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE",
  PENDING = "PENDING",
  EXPIRED = "EXPIRED",
}

// Backend DTO: AllowanceTypeDto
export interface AllowanceTypeDto {
  id: string;
  businessId: string;
  allowanceName: string;
  allowanceCode: string;
  calculationMethod: AllowanceCalculationMethod;
  isTaxable: boolean;
  taxRateId?: string;
  amount?: number;
  isActive: boolean;
  isEPFETFEligible: boolean;
  description?: string;
  createdBy: string;
  updatedBy?: string;
  deletedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// Backend DTO: CreateAllowanceTypeDto
export interface CreateAllowanceTypeDto {
  allowanceName: string;
  allowanceCode: string;
  calculationMethod: AllowanceCalculationMethod;
  isTaxable?: boolean;
  taxRateId?: string;
  amount?: number;
  isActive?: boolean;
  isEPFETFEligible?: boolean;
  description?: string;
}

// Backend DTO: UpdateAllowanceTypeDto
export interface UpdateAllowanceTypeDto {
  allowanceName?: string;
  allowanceCode?: string;
  calculationMethod?: AllowanceCalculationMethod;
  isTaxable?: boolean;
  taxRateId?: string;
  amount?: number;
  isActive?: boolean;
  isEPFETFEligible?: boolean;
  description?: string;
}

// Backend DTO: AllowanceTypeListDto (for table display)
export interface AllowanceTypeListDto {
  id: string;
  allowanceName: string;
  allowanceCode: string;
  calculationMethod: AllowanceCalculationMethod;
  isTaxable: boolean;
  taxRateId?: string;
  amount?: number;
  isActive: boolean;
  isEPFETFEligible: boolean;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Backend DTO: PaginatedAllowanceTypesListResponseDto
export interface PaginationMetaDto {
  total: number;
  page: number;
  totalPages: number;
}

export interface PaginatedAllowanceTypesListResponseDto {
  data: AllowanceTypeListDto[];
  meta: PaginationMetaDto;
}

// Backend DTO: AllowanceTypeIdResponseDto
export interface AllowanceTypeIdResponseDto {
  id: string;
}

// Backend DTO: BulkAllowanceTypeIdsResponseDto
export interface BulkAllowanceTypeIdsResponseDto {
  ids: string[];
}

// Backend DTO: BulkDeleteAllowanceTypeResponseDto
export interface BulkDeleteAllowanceTypeResponseDto {
  deleted: number;
  message: string;
  deletedIds: string[];
}

// Backend DTO: BulkUpdateAllowanceTypeStatusDto
export interface BulkUpdateAllowanceTypeStatusDto {
  allowanceTypeIds: string[];
  isActive: boolean;
}

// Backend DTO: BulkUpdateAllowanceTypeStatusResponseDto
export interface BulkUpdateAllowanceTypeStatusResponseDto {
  updated: number;
  message: string;
  updatedIds: string[];
  failed?: Array<{
    allowanceTypeId: string;
    error: string;
  }>;
}

// Backend DTO: AllowanceTypeCodeAvailabilityResponseDto
export interface AllowanceTypeCodeAvailabilityResponseDto {
  available: boolean;
}

// Backend DTO: BulkDeleteAllowanceTypeDto
export interface BulkDeleteAllowanceTypeDto {
  allowanceTypeIds: string[];
}

// API Response wrappers
export interface AllowanceTypeResponse extends ApiResponse<AllowanceTypeDto> {}

export interface AllowanceTypeListResponse
  extends ApiResponse<AllowanceTypeListDto[]> {}

export interface AllowanceTypePaginatedResponse
  extends ApiResponse<PaginatedAllowanceTypesListResponseDto | null> {}

export interface AllowanceTypeIdResponse
  extends ApiResponse<AllowanceTypeIdResponseDto> {}

export interface BulkAllowanceTypeIdsResponse
  extends ApiResponse<BulkAllowanceTypeIdsResponseDto> {}

export interface BulkDeleteAllowanceTypeResponse
  extends ApiResponse<BulkDeleteAllowanceTypeResponseDto> {}

export interface BulkUpdateAllowanceTypeStatusResponse
  extends ApiResponse<BulkUpdateAllowanceTypeStatusResponseDto> {}

export interface AllowanceTypeCodeAvailabilityResponse
  extends ApiResponse<AllowanceTypeCodeAvailabilityResponseDto> {}

// Table data interface - optimized for table display
export interface AllowanceTypeTableData {
  id: string;
  allowanceName: string;
  allowanceCode: string;
  calculationMethod: AllowanceCalculationMethod;
  isTaxable: boolean;
  taxRateId?: string;
  amount?: number;
  isActive: boolean;
  isEPFETFEligible: boolean;
  description?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Form values interface for UI components
export interface AllowanceTypeFormValues {
  allowanceName: string;
  allowanceCode: string;
  calculationMethod: AllowanceCalculationMethod;
  isTaxable?: boolean;
  taxRateId?: string;
  amount?: number;
  isActive?: boolean;
  isEPFETFEligible?: boolean;
  description?: string;
}

// Bulk create allowance type interface
export interface BulkCreateAllowanceTypeDto extends CreateAllowanceTypeDto {}

// Legacy aliases for backward compatibility (to be removed gradually)
export type AllowanceType = AllowanceTypeDto;
export type AllowanceTypePaginatedData = PaginatedAllowanceTypesListResponseDto;

// Extended interfaces for frontend use
export interface AllowanceTypeTableDataExtended extends AllowanceTypeTableData {
  _id: string; // Legacy field for backward compatibility
  publicId: string; // Legacy field for backward compatibility
}
