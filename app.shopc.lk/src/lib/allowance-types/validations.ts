import { z } from "zod";
import { AllowanceCalculationMethod } from "@/types/allowance-type";

// Backend DTO: CreateAllowanceTypeDto
export const createAllowanceTypeSchema = z.object({
  allowanceName: z.string().min(1, "Allowance name is required"),
  allowanceCode: z.string().min(1, "Allowance code is required"),
  calculationMethod: z.nativeEnum(AllowanceCalculationMethod, {
    errorMap: () => ({ message: "Invalid calculation method" }),
  }),
  isTaxable: z.boolean().optional().default(true),
  taxRateId: z.string().uuid("Invalid tax rate ID").optional(),
  amount: z.number().min(0, "Amount must be non-negative").optional(),
  isActive: z.boolean().optional().default(true),
  isEPFETFEligible: z.boolean().optional().default(false),
  description: z.string().optional(),
});

// Backend DTO: UpdateAllowanceTypeDto
export const updateAllowanceTypeSchema = z.object({
  allowanceName: z.string().min(1, "Allowance name is required").optional(),
  allowanceCode: z.string().min(1, "Allowance code is required").optional(),
  calculationMethod: z.nativeEnum(AllowanceCalculationMethod, {
    errorMap: () => ({ message: "Invalid calculation method" }),
  }).optional(),
  isTaxable: z.boolean().optional(),
  taxRateId: z.string().uuid("Invalid tax rate ID").optional(),
  amount: z.number().min(0, "Amount must be non-negative").optional(),
  isActive: z.boolean().optional(),
  isEPFETFEligible: z.boolean().optional(),
  description: z.string().optional(),
});

// Schema for getting allowance types with pagination and filters
export const getAllowanceTypesSchema = z.object({
  page: z.number().min(1).default(1),
  perPage: z.number().min(1).default(10),
  allowanceName: z.string().optional(),
  allowanceCode: z.string().optional(),
  calculationMethod: z.string().optional(),
  isTaxable: z.string().optional(),
  isActive: z.string().optional(),
  from: z.string().optional(),
  to: z.string().optional(),
  filters: z
    .array(
      z.object({
        id: z.string(),
        value: z.union([z.string(), z.array(z.string())]),
        operator: z
          .enum([
            "iLike",
            "notILike",
            "eq",
            "ne",
            "isEmpty",
            "isNotEmpty",
            "lt",
            "lte",
            "gt",
            "gte",
            "isBetween",
            "isRelativeToToday",
          ])
          .default("iLike"),
        type: z
          .enum(["text", "number", "date", "boolean", "select", "multi-select"])
          .default("text"),
        rowId: z.string().optional(),
      })
    )
    .optional()
    .default([]),
  joinOperator: z.enum(["and", "or"]).default("and"),
  sort: z
    .array(
      z.object({
        id: z.string(),
        desc: z.boolean(),
      })
    )
    .optional()
    .default([]),
});

// Schema for bulk create allowance types
export const bulkCreateAllowanceTypesSchema = z.object({
  allowanceTypes: z.array(createAllowanceTypeSchema).min(1, "At least one allowance type is required"),
});

// Schema for bulk delete allowance types
export const bulkDeleteAllowanceTypesSchema = z.object({
  allowanceTypeIds: z.array(z.string().uuid("Invalid allowance type ID")).min(1, "At least one allowance type ID is required"),
});

// Schema for bulk update allowance type status
export const bulkUpdateAllowanceTypeStatusSchema = z.object({
  allowanceTypeIds: z.array(z.string().uuid("Invalid allowance type ID")).min(1, "At least one allowance type ID is required"),
  isActive: z.boolean(),
});

// Schema for checking allowance type code availability
export const checkAllowanceTypeCodeAvailabilitySchema = z.object({
  allowanceCode: z.string().min(1, "Allowance code is required"),
  excludeId: z.string().uuid("Invalid allowance type ID").optional(),
});

// Schema for importing allowance types from CSV/Excel
export const importAllowanceTypeSchema = z.object({
  allowanceName: z.string().min(1, "Allowance name is required"),
  allowanceCode: z.string().min(1, "Allowance code is required"),
  calculationMethod: z.string().min(1, "Calculation method is required"),
  isTaxable: z.string().optional(),
  description: z.string().optional(),
});

// Schema for bulk import allowance types
export const bulkImportAllowanceTypesSchema = z.object({
  allowanceTypes: z.array(importAllowanceTypeSchema).min(1, "At least one allowance type is required"),
});

// Type exports for use in components
export type CreateAllowanceTypeSchema = z.infer<typeof createAllowanceTypeSchema>;
export type UpdateAllowanceTypeSchema = z.infer<typeof updateAllowanceTypeSchema>;
export type GetAllowanceTypesSchema = z.infer<typeof getAllowanceTypesSchema>;
export type BulkCreateAllowanceTypesSchema = z.infer<typeof bulkCreateAllowanceTypesSchema>;
export type BulkDeleteAllowanceTypesSchema = z.infer<typeof bulkDeleteAllowanceTypesSchema>;
export type BulkUpdateAllowanceTypeStatusSchema = z.infer<typeof bulkUpdateAllowanceTypeStatusSchema>;
export type CheckAllowanceTypeCodeAvailabilitySchema = z.infer<typeof checkAllowanceTypeCodeAvailabilitySchema>;
export type ImportAllowanceTypeSchema = z.infer<typeof importAllowanceTypeSchema>;
export type BulkImportAllowanceTypesSchema = z.infer<typeof bulkImportAllowanceTypesSchema>;
