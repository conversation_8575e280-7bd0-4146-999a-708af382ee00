import {
  AllowanceTypeDto,
  AllowanceTypeListDto,
  AllowanceTypeTableData,
  AllowanceCalculationMethod,
  AllowanceTypePaginatedResponse,
  AllowanceTypeIdResponse,
  BulkAllowanceTypeIdsResponse,
  BulkDeleteAllowanceTypeResponse,
  AllowanceTypeCodeAvailabilityResponse,
  AllowanceTypeListResponse,
  AllowanceTypeResponse,
  CreateAllowanceTypeDto,
  UpdateAllowanceTypeDto,
  BulkCreateAllowanceTypeDto,
  PaginatedAllowanceTypesListResponseDto,
  BulkUpdateAllowanceTypeStatusResponse,
} from "@/types/allowance-type";
import { ApiStatus, ApiResponse } from "@/types/common";
import { GetAllowanceTypesSchema } from "./validations";

// Raw demo allowance types data
const rawDemoAllowanceTypes = [
  {
    id: "allowance_1",
    businessId: "biz_1",
    allowanceName: "Housing Allowance",
    allowanceCode: "HOUSING",
    calculationMethod: AllowanceCalculationMethod.FIXED,
    isTaxable: true,
    amount: 100000,
    isActive: true,
    isEPFETFEligible: false,
    description: "Monthly housing allowance for employees",
    createdBy: "user_1",
    updatedBy: "user_1",
    createdAt: new Date("2023-01-01T00:00:00Z"),
    updatedAt: new Date("2023-01-01T00:00:00Z"),
  },
  {
    id: "allowance_2",
    businessId: "biz_1",
    allowanceName: "Transport Allowance",
    allowanceCode: "TRANSPORT",
    calculationMethod: AllowanceCalculationMethod.PERCENTAGE,
    isTaxable: false,
    amount: 15000,
    isActive: true,
    isEPFETFEligible: false,
    description: "Transportation allowance based on basic salary",
    createdBy: "user_1",
    updatedBy: "user_1",
    createdAt: new Date("2023-01-02T00:00:00Z"),
    updatedAt: new Date("2023-01-02T00:00:00Z"),
  },
  {
    id: "allowance_3",
    businessId: "biz_1",
    allowanceName: "Meal Allowance",
    allowanceCode: "MEAL",
    calculationMethod: AllowanceCalculationMethod.FIXED,
    isTaxable: true,
    taxRateId: "tax_1",
    amount: 50000,
    isActive: true,
    isEPFETFEligible: false,
    description: "Daily meal allowance for employees",
    createdBy: "user_1",
    updatedBy: "user_1",
    createdAt: new Date("2023-01-03T00:00:00Z"),
    updatedAt: new Date("2023-01-03T00:00:00Z"),
  },
  {
    id: "allowance_4",
    businessId: "biz_1",
    allowanceName: "Medical Allowance",
    allowanceCode: "MEDICAL",
    calculationMethod: AllowanceCalculationMethod.SLAB,
    isTaxable: false,
    amount: 30000,
    isActive: true,
    isEPFETFEligible: false,
    description: "Medical allowance based on employee grade",
    createdBy: "user_1",
    updatedBy: "user_1",
    createdAt: new Date("2023-01-04T00:00:00Z"),
    updatedAt: new Date("2023-01-04T00:00:00Z"),
  },
  {
    id: "allowance_5",
    businessId: "biz_1",
    allowanceName: "Performance Bonus",
    allowanceCode: "PERFORMANCE",
    calculationMethod: AllowanceCalculationMethod.FORMULA,
    isTaxable: true,
    taxRateId: "tax_2",
    amount: 25000,
    isActive: false,
    isEPFETFEligible: false,
    description: "Performance-based bonus calculation",
    createdBy: "user_1",
    updatedBy: "user_1",
    createdAt: new Date("2023-01-05T00:00:00Z"),
    updatedAt: new Date("2023-01-05T00:00:00Z"),
  },
];

// Convert raw data to AllowanceTypeDto format
function convertToAllowanceTypeDto(rawData: any): AllowanceTypeDto {
  return {
    ...rawData,
    deletedAt: undefined,
  } as AllowanceTypeDto;
}

// Convert raw data to AllowanceTypeListDto format
function convertToAllowanceTypeListDto(rawData: any): AllowanceTypeListDto {
  return {
    id: rawData.id,
    allowanceName: rawData.allowanceName,
    allowanceCode: rawData.allowanceCode,
    calculationMethod: rawData.calculationMethod,
    isTaxable: rawData.isTaxable,
    taxRateId: rawData.taxRateId,
    amount: rawData.amount,
    isActive: rawData.isActive,
    description: rawData.description,
    createdAt: rawData.createdAt,
    updatedAt: rawData.updatedAt,
  };
}

// Convert raw data to AllowanceTypeTableData format
function convertToAllowanceTypeTableData(rawData: any): AllowanceTypeTableData {
  return {
    id: rawData.id,
    allowanceName: rawData.allowanceName,
    allowanceCode: rawData.allowanceCode,
    calculationMethod: rawData.calculationMethod,
    isTaxable: rawData.isTaxable,
    taxRateId: rawData.taxRateId,
    amount: rawData.amount,
    isActive: rawData.isActive,
    description: rawData.description,
    createdAt: rawData.createdAt,
    updatedAt: rawData.updatedAt,
  };
}

// Demo allowance types data
export const demoAllowanceTypes: AllowanceTypeDto[] = rawDemoAllowanceTypes.map(
  convertToAllowanceTypeDto
);

// Demo allowance types list data
export const demoAllowanceTypesList: AllowanceTypeListDto[] =
  rawDemoAllowanceTypes.map(convertToAllowanceTypeListDto);

// Demo allowance types table data
export const demoAllowanceTypesTableData: AllowanceTypeTableData[] =
  rawDemoAllowanceTypes.map(convertToAllowanceTypeTableData);

// Helper function to filter allowance types based on search parameters
function filterAllowanceTypes(
  allowanceTypes: AllowanceTypeTableData[],
  params: GetAllowanceTypesSchema
): AllowanceTypeTableData[] {
  let filtered = [...allowanceTypes];

  // Filter by allowance name
  if (params.allowanceName) {
    filtered = filtered.filter((allowanceType) =>
      allowanceType.allowanceName
        .toLowerCase()
        .includes(params.allowanceName!.toLowerCase())
    );
  }

  // Filter by allowance code
  if (params.allowanceCode) {
    filtered = filtered.filter((allowanceType) =>
      allowanceType.allowanceCode
        .toLowerCase()
        .includes(params.allowanceCode!.toLowerCase())
    );
  }

  // Filter by calculation method
  if (params.calculationMethod) {
    filtered = filtered.filter(
      (allowanceType) =>
        allowanceType.calculationMethod === params.calculationMethod
    );
  }

  // Filter by taxable status
  if (params.isTaxable !== undefined) {
    const isTaxable = params.isTaxable === "true";
    filtered = filtered.filter(
      (allowanceType) => allowanceType.isTaxable === isTaxable
    );
  }

  // Filter by active status
  if (params.isActive !== undefined) {
    const isActive = params.isActive === "true";
    filtered = filtered.filter(
      (allowanceType) => allowanceType.isActive === isActive
    );
  }

  // Apply date range filter
  if (params.from || params.to) {
    const fromDate = params.from ? new Date(params.from) : null;
    const toDate = params.to ? new Date(params.to) : null;

    filtered = filtered.filter((allowanceType) => {
      const createdAt = new Date(allowanceType.createdAt);
      if (fromDate && createdAt < fromDate) return false;
      if (toDate && createdAt > toDate) return false;
      return true;
    });
  }

  return filtered;
}

// Helper function to sort allowance types
function sortAllowanceTypes(
  allowanceTypes: AllowanceTypeTableData[],
  sort: { id: string; desc: boolean }[]
): AllowanceTypeTableData[] {
  if (!sort || sort.length === 0) {
    return allowanceTypes.sort((a, b) => a.allowanceName.localeCompare(b.allowanceName));
  }

  return [...allowanceTypes].sort((a, b) => {
    for (const sortItem of sort) {
      const { id, desc } = sortItem;
      let aValue = (a as any)[id];
      let bValue = (b as any)[id];

      if (aValue === bValue) continue;

      if (aValue == null) return desc ? -1 : 1;
      if (bValue == null) return desc ? 1 : -1;

      if (typeof aValue === "string" && typeof bValue === "string") {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      const comparison = aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      return desc ? -comparison : comparison;
    }
    return 0;
  });
}

// Demo API functions
export async function getDemoAllowanceTypesTableData(
  params: GetAllowanceTypesSchema
): Promise<AllowanceTypePaginatedResponse> {
  await new Promise((resolve) => setTimeout(resolve, 500)); // Simulate API delay

  try {
    let filtered = filterAllowanceTypes(demoAllowanceTypesTableData, params);
    const sorted = sortAllowanceTypes(filtered, params.sort || []);

    const total = sorted.length;
    const totalPages = Math.ceil(total / params.perPage);
    const startIndex = (params.page - 1) * params.perPage;
    const endIndex = startIndex + params.perPage;
    const paginatedData = sorted.slice(startIndex, endIndex);

    const response: PaginatedAllowanceTypesListResponseDto = {
      data: paginatedData,
      meta: {
        total,
        page: params.page,
        totalPages,
      },
    };

    return {
      status: ApiStatus.SUCCESS,
      message: "Allowance types retrieved successfully",
      data: response,
    };
  } catch (error) {
    return {
      status: ApiStatus.FAIL,
      message: "Failed to retrieve allowance types",
      data: null,
    };
  }
}

export async function getDemoAllowanceType(
  id: string
): Promise<AllowanceTypeResponse> {
  await new Promise((resolve) => setTimeout(resolve, 300)); // Simulate API delay

  const allowanceType = demoAllowanceTypes.find((at) => at.id === id);

  if (!allowanceType) {
    return {
      status: ApiStatus.FAIL,
      message: "Allowance type not found",
      data: null,
    };
  }

  return {
    status: ApiStatus.SUCCESS,
    message: "Allowance type retrieved successfully",
    data: allowanceType,
  };
}

export async function createDemoAllowanceType(
  data: CreateAllowanceTypeDto
): Promise<AllowanceTypeIdResponse> {
  await new Promise((resolve) => setTimeout(resolve, 800)); // Simulate API delay

  // Check if allowance code already exists
  const existingAllowanceType = demoAllowanceTypes.find(
    (at) => at.allowanceCode.toLowerCase() === data.allowanceCode.toLowerCase()
  );

  if (existingAllowanceType) {
    return {
      status: ApiStatus.FAIL,
      message: "Allowance type code already exists",
      data: null,
    };
  }

  const newId = `allowance_${Date.now()}`;
  const newAllowanceType: AllowanceTypeDto = {
    id: newId,
    businessId: "biz_1",
    allowanceName: data.allowanceName,
    allowanceCode: data.allowanceCode,
    calculationMethod: data.calculationMethod,
    isTaxable: data.isTaxable ?? true,
    taxRateId: data.taxRateId,
    amount: data.amount,
    isActive: data.isActive ?? true,
    description: data.description,
    createdBy: "user_1",
    updatedBy: "user_1",
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  // Add to demo data
  demoAllowanceTypes.push(newAllowanceType);
  demoAllowanceTypesList.push(convertToAllowanceTypeListDto(newAllowanceType));
  demoAllowanceTypesTableData.push(
    convertToAllowanceTypeTableData(newAllowanceType)
  );

  return {
    status: ApiStatus.SUCCESS,
    message: "Allowance type created successfully",
    data: { id: newId },
  };
}

export async function updateDemoAllowanceType(
  id: string,
  data: UpdateAllowanceTypeDto
): Promise<AllowanceTypeIdResponse> {
  await new Promise((resolve) => setTimeout(resolve, 600)); // Simulate API delay

  const allowanceTypeIndex = demoAllowanceTypes.findIndex((at) => at.id === id);

  if (allowanceTypeIndex === -1) {
    return {
      status: ApiStatus.FAIL,
      message: "Allowance type not found",
      data: null,
    };
  }

  // Check if allowance code already exists (excluding current allowance type)
  if (data.allowanceCode) {
    const existingAllowanceType = demoAllowanceTypes.find(
      (at) =>
        at.id !== id &&
        at.allowanceCode.toLowerCase() === data.allowanceCode!.toLowerCase()
    );

    if (existingAllowanceType) {
      return {
        status: ApiStatus.FAIL,
        message: "Allowance type code already exists",
        data: null,
      };
    }
  }

  // Update the allowance type
  const updatedAllowanceType = {
    ...demoAllowanceTypes[allowanceTypeIndex],
    ...data,
    updatedAt: new Date(),
  };

  demoAllowanceTypes[allowanceTypeIndex] = updatedAllowanceType;
  demoAllowanceTypesList[allowanceTypeIndex] =
    convertToAllowanceTypeListDto(updatedAllowanceType);
  demoAllowanceTypesTableData[allowanceTypeIndex] =
    convertToAllowanceTypeTableData(updatedAllowanceType);

  return {
    status: ApiStatus.SUCCESS,
    message: "Allowance type updated successfully",
    data: { id },
  };
}

export async function deleteDemoAllowanceType(
  id: string
): Promise<ApiResponse<{ message: string }>> {
  await new Promise((resolve) => setTimeout(resolve, 400)); // Simulate API delay

  const allowanceTypeIndex = demoAllowanceTypes.findIndex((at) => at.id === id);

  if (allowanceTypeIndex === -1) {
    return {
      status: ApiStatus.FAIL,
      message: "Allowance type not found",
      data: null,
    };
  }

  // Remove from demo data
  demoAllowanceTypes.splice(allowanceTypeIndex, 1);
  demoAllowanceTypesList.splice(allowanceTypeIndex, 1);
  demoAllowanceTypesTableData.splice(allowanceTypeIndex, 1);

  return {
    status: ApiStatus.SUCCESS,
    message: "Allowance type deleted successfully",
    data: { message: "Allowance type deleted successfully" },
  };
}

export async function bulkCreateDemoAllowanceTypes(
  allowanceTypes: BulkCreateAllowanceTypeDto[]
): Promise<BulkAllowanceTypeIdsResponse> {
  await new Promise((resolve) => setTimeout(resolve, 1000)); // Simulate API delay

  const newIds: string[] = [];
  const errors: string[] = [];

  for (const data of allowanceTypes) {
    // Check if allowance code already exists
    const existingAllowanceType = demoAllowanceTypes.find(
      (at) =>
        at.allowanceCode.toLowerCase() === data.allowanceCode.toLowerCase()
    );

    if (existingAllowanceType) {
      errors.push(`Allowance type code '${data.allowanceCode}' already exists`);
      continue;
    }

    const newId = `allowance_${Date.now()}_${Math.random()
      .toString(36)
      .substring(2, 11)}`;
    const newAllowanceType: AllowanceTypeDto = {
      id: newId,
      businessId: "biz_1",
      allowanceName: data.allowanceName,
      allowanceCode: data.allowanceCode,
      calculationMethod: data.calculationMethod,
        isTaxable: data.isTaxable ?? true,
      taxRateId: data.taxRateId,
    amount: data.amount,
      isActive: data.isActive ?? true,
      description: data.description,
      createdBy: "user_1",
      updatedBy: "user_1",
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Add to demo data
    demoAllowanceTypes.push(newAllowanceType);
    demoAllowanceTypesList.push(
      convertToAllowanceTypeListDto(newAllowanceType)
    );
    demoAllowanceTypesTableData.push(
      convertToAllowanceTypeTableData(newAllowanceType)
    );
    newIds.push(newId);
  }

  if (errors.length > 0) {
    return {
      status: ApiStatus.FAIL,
      message: errors.join(", "),
      data: null,
    };
  }

  return {
    status: ApiStatus.SUCCESS,
    message: `${newIds.length} allowance types created successfully`,
    data: { ids: newIds },
  };
}

export async function bulkDeleteDemoAllowanceTypes(
  allowanceTypeIds: string[]
): Promise<BulkDeleteAllowanceTypeResponse> {
  await new Promise((resolve) => setTimeout(resolve, 600)); // Simulate API delay

  const deletedIds: string[] = [];
  const notFoundIds: string[] = [];

  for (const id of allowanceTypeIds) {
    const allowanceTypeIndex = demoAllowanceTypes.findIndex(
      (at) => at.id === id
    );

    if (allowanceTypeIndex === -1) {
      notFoundIds.push(id);
      continue;
    }

    // Remove from demo data
    demoAllowanceTypes.splice(allowanceTypeIndex, 1);
    demoAllowanceTypesList.splice(allowanceTypeIndex, 1);
    demoAllowanceTypesTableData.splice(allowanceTypeIndex, 1);
    deletedIds.push(id);
  }

  return {
    status: ApiStatus.SUCCESS,
    message: `${deletedIds.length} allowance types deleted successfully`,
    data: {
      deleted: deletedIds.length,
      message: `${deletedIds.length} allowance types deleted successfully`,
      deletedIds,
    },
  };
}

export async function bulkUpdateDemoAllowanceTypeStatus(
  allowanceTypeIds: string[],
  isActive: boolean
): Promise<BulkUpdateAllowanceTypeStatusResponse> {
  await new Promise((resolve) => setTimeout(resolve, 500)); // Simulate API delay

  const updatedIds: string[] = [];
  const notFoundIds: string[] = [];

  for (const id of allowanceTypeIds) {
    const allowanceTypeIndex = demoAllowanceTypes.findIndex(
      (at) => at.id === id
    );

    if (allowanceTypeIndex === -1) {
      notFoundIds.push(id);
      continue;
    }

    // Update the allowance type status
    const updatedAllowanceType = {
      ...demoAllowanceTypes[allowanceTypeIndex],
      isActive,
      updatedAt: new Date(),
    };

    demoAllowanceTypes[allowanceTypeIndex] = updatedAllowanceType;
    demoAllowanceTypesList[allowanceTypeIndex] =
      convertToAllowanceTypeListDto(updatedAllowanceType);
    demoAllowanceTypesTableData[allowanceTypeIndex] =
      convertToAllowanceTypeTableData(updatedAllowanceType);
    updatedIds.push(id);
  }

  return {
    status: ApiStatus.SUCCESS,
    message: `${updatedIds.length} allowance types updated successfully`,
    data: {
      updated: updatedIds.length,
      message: `${updatedIds.length} allowance types updated successfully`,
      updatedIds,
    },
  };
}

export async function checkDemoAllowanceTypeCodeAvailability(
  allowanceCode: string,
  excludeId?: string
): Promise<AllowanceTypeCodeAvailabilityResponse> {
  await new Promise((resolve) => setTimeout(resolve, 200)); // Simulate API delay

  const existingAllowanceType = demoAllowanceTypes.find(
    (at) =>
      at.id !== excludeId &&
      at.allowanceCode.toLowerCase() === allowanceCode.toLowerCase()
  );

  return {
    status: ApiStatus.SUCCESS,
    message: "Availability checked successfully",
    data: { available: !existingAllowanceType },
  };
}
