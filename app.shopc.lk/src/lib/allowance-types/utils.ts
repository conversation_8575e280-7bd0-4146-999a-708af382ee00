import {
  AllowanceTypeDto,
  AllowanceTypeTableData,
  AllowanceCalculationMethod,
} from "@/types/allowance-type";

// Format calculation method for display
export function formatCalculationMethod(method: AllowanceCalculationMethod): string {
  switch (method) {
    case AllowanceCalculationMethod.FIXED:
      return "Fixed Amount";
    case AllowanceCalculationMethod.PERCENTAGE:
      return "Percentage";
    case AllowanceCalculationMethod.FORMULA:
      return "Formula Based";
    case AllowanceCalculationMethod.SLAB:
      return "Slab Based";
    default:
      return "Unknown";
  }
}

// Get calculation method color for badges/indicators
export function getCalculationMethodColor(method: AllowanceCalculationMethod): string {
  switch (method) {
    case AllowanceCalculationMethod.FIXED:
      return "bg-blue-100 text-blue-800";
    case AllowanceCalculationMethod.PERCENTAGE:
      return "bg-green-100 text-green-800";
    case AllowanceCalculationMethod.FORMULA:
      return "bg-purple-100 text-purple-800";
    case AllowanceCalculationMethod.SLAB:
      return "bg-orange-100 text-orange-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
}

// Format active status for display
export function formatActiveStatus(isActive: boolean): string {
  return isActive ? "Active" : "Inactive";
}

// Get active status color for badges/indicators
export function getActiveStatusColor(isActive: boolean): string {
  return isActive
    ? "bg-green-100 text-green-800"
    : "bg-red-100 text-red-800";
}

// Format taxable status for display
export function formatTaxableStatus(isTaxable: boolean): string {
  return isTaxable ? "Taxable" : "Non-Taxable";
}

// Get taxable status color for badges/indicators
export function getTaxableStatusColor(isTaxable: boolean): string {
  return isTaxable
    ? "bg-yellow-100 text-yellow-800"
    : "bg-gray-100 text-gray-800";
}

// Generate allowance code from name
export function generateAllowanceCode(name: string): string {
  return name
    .toUpperCase()
    .replace(/[^A-Z0-9\s]/g, "") // Remove special characters
    .replace(/\s+/g, "_") // Replace spaces with underscores
    .replace(/_+/g, "_") // Replace multiple underscores with single
    .replace(/^_|_$/g, "") // Remove leading/trailing underscores
    .substring(0, 10); // Limit to 10 characters
}

// Validate allowance code format
export function isValidAllowanceCode(code: string): boolean {
  // Allow alphanumeric characters and underscores, 2-20 characters
  const codeRegex = /^[A-Z0-9_]{2,20}$/;
  return codeRegex.test(code);
}

// Sort allowance types by name
export function sortAllowanceTypesByName(
  allowanceTypes: (AllowanceTypeDto | AllowanceTypeTableData)[]
): (AllowanceTypeDto | AllowanceTypeTableData)[] {
  return [...allowanceTypes].sort((a, b) => a.allowanceName.localeCompare(b.allowanceName));
}

// Filter allowance types by active status
export function filterAllowanceTypesByStatus(
  allowanceTypes: (AllowanceTypeDto | AllowanceTypeTableData)[],
  isActive: boolean
): (AllowanceTypeDto | AllowanceTypeTableData)[] {
  return allowanceTypes.filter((allowanceType) => allowanceType.isActive === isActive);
}

// Filter allowance types by calculation method
export function filterAllowanceTypesByMethod(
  allowanceTypes: (AllowanceTypeDto | AllowanceTypeTableData)[],
  method: AllowanceCalculationMethod
): (AllowanceTypeDto | AllowanceTypeTableData)[] {
  return allowanceTypes.filter((allowanceType) => allowanceType.calculationMethod === method);
}

// Filter allowance types by taxable status
export function filterAllowanceTypesByTaxable(
  allowanceTypes: (AllowanceTypeDto | AllowanceTypeTableData)[],
  isTaxable: boolean
): (AllowanceTypeDto | AllowanceTypeTableData)[] {
  return allowanceTypes.filter((allowanceType) => allowanceType.isTaxable === isTaxable);
}

// Search allowance types by name or code
export function searchAllowanceTypes(
  allowanceTypes: (AllowanceTypeDto | AllowanceTypeTableData)[],
  searchTerm: string
): (AllowanceTypeDto | AllowanceTypeTableData)[] {
  if (!searchTerm.trim()) {
    return allowanceTypes;
  }

  const term = searchTerm.toLowerCase();
  return allowanceTypes.filter(
    (allowanceType) =>
      allowanceType.allowanceName.toLowerCase().includes(term) ||
      allowanceType.allowanceCode.toLowerCase().includes(term) ||
      (allowanceType.description && allowanceType.description.toLowerCase().includes(term))
  );
}

// Get unique calculation methods from allowance types
export function getUniqueCalculationMethods(
  allowanceTypes: (AllowanceTypeDto | AllowanceTypeTableData)[]
): AllowanceCalculationMethod[] {
  const methods = new Set(allowanceTypes.map((at) => at.calculationMethod));
  return Array.from(methods);
}

// Get allowance types statistics
export function getAllowanceTypesStats(
  allowanceTypes: (AllowanceTypeDto | AllowanceTypeTableData)[]
): {
  total: number;
  active: number;
  inactive: number;
  taxable: number;
  nonTaxable: number;
  byMethod: Record<AllowanceCalculationMethod, number>;
} {
  const stats = {
    total: allowanceTypes.length,
    active: 0,
    inactive: 0,
    taxable: 0,
    nonTaxable: 0,
    byMethod: {
      [AllowanceCalculationMethod.FIXED]: 0,
      [AllowanceCalculationMethod.PERCENTAGE]: 0,
      [AllowanceCalculationMethod.FORMULA]: 0,
      [AllowanceCalculationMethod.SLAB]: 0,
    },
  };

  allowanceTypes.forEach((allowanceType) => {
    // Count by status
    if (allowanceType.isActive) {
      stats.active++;
    } else {
      stats.inactive++;
    }

    // Count by taxable status
    if (allowanceType.isTaxable) {
      stats.taxable++;
    } else {
      stats.nonTaxable++;
    }

    // Count by calculation method
    stats.byMethod[allowanceType.calculationMethod]++;
  });

  return stats;
}

// Validate allowance type data
export function validateAllowanceTypeData(data: {
  allowanceName: string;
  allowanceCode: string;
  calculationMethod: AllowanceCalculationMethod;
}): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  // Validate allowance name
  if (!data.allowanceName || data.allowanceName.trim().length === 0) {
    errors.push("Allowance name is required");
  } else if (data.allowanceName.length > 191) {
    errors.push("Allowance name must be less than 191 characters");
  }

  // Validate allowance code
  if (!data.allowanceCode || data.allowanceCode.trim().length === 0) {
    errors.push("Allowance code is required");
  } else if (!isValidAllowanceCode(data.allowanceCode)) {
    errors.push("Allowance code must be 2-20 characters long and contain only letters, numbers, and underscores");
  }

  // Validate calculation method
  if (!Object.values(AllowanceCalculationMethod).includes(data.calculationMethod)) {
    errors.push("Invalid calculation method");
  }


  return {
    isValid: errors.length === 0,
    errors,
  };
}

// Format date for display
export function formatDate(date: Date | string): string {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  return dateObj.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
}

// Format datetime for display
export function formatDateTime(date: Date | string): string {
  const dateObj = typeof date === "string" ? new Date(date) : date;
  return dateObj.toLocaleString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
}
