import {
  DeductionTypeDto,
  DeductionTypeSlimDto,
  DeductionTypeListDto,
  CreateDeductionTypeDto,
  DeductionTypeIdResponse,
  DeductionTypeResponse,
  BulkDeductionTypeIdsResponse,
  BulkDeleteDeductionTypeResponse,
  DeductionTypeCodeAvailabilityResponse,
  SimpleDeductionTypeResponse,
  DeductionTypeListPaginatedResponse,
  DeductionCalculationMethod,
  DeductionAppliesTo,
  BulkUpdateDeductionTypeStatusResponse,
} from "@/types/deduction-type";
import { ApiStatus } from "@/types/common";
import { GetDeductionTypesSchema } from "./validations";

// Demo deduction types data
const demoDeductionTypes: DeductionTypeDto[] = [
  {
    id: "550e8400-e29b-41d4-a716-446655440000",
    businessId: "business-1",
    deductionName: "Income Tax",
    deductionCode: "IT001",
    calculationMethod: DeductionCalculationMethod.PERCENTAGE,
    amount: 15.5,
    isSystemDefined: true,
    isActive: true,
    appliesTo: DeductionAppliesTo.GROSS,
    description: "Monthly income tax deduction",
    createdBy: "<PERSON> Doe",
    updatedBy: "Jane Smith",
    createdAt: new Date("2023-01-01T00:00:00Z"),
    updatedAt: new Date("2023-01-15T00:00:00Z"),
  },
  {
    id: "550e8400-e29b-41d4-a716-446655440001",
    businessId: "business-1",
    deductionName: "Health Insurance",
    deductionCode: "HI001",
    calculationMethod: DeductionCalculationMethod.FIXED,
    amount: 250.0,
    isSystemDefined: false,
    isActive: true,
    appliesTo: DeductionAppliesTo.GROSS,
    description: "Employee health insurance premium",
    createdBy: "John Doe",
    createdAt: new Date("2023-01-02T00:00:00Z"),
    updatedAt: new Date("2023-01-02T00:00:00Z"),
  },
  {
    id: "550e8400-e29b-41d4-a716-446655440002",
    businessId: "business-1",
    deductionName: "Pension Fund",
    deductionCode: "PF001",
    calculationMethod: DeductionCalculationMethod.PERCENTAGE,
    amount: 8.0,
    isSystemDefined: true,
    isActive: true,
    appliesTo: DeductionAppliesTo.BASIC,
    description: "Employee pension fund contribution",
    createdBy: "Jane Smith",
    createdAt: new Date("2023-01-03T00:00:00Z"),
    updatedAt: new Date("2023-01-03T00:00:00Z"),
  },
  {
    id: "550e8400-e29b-41d4-a716-446655440003",
    businessId: "business-1",
    deductionName: "Union Dues",
    deductionCode: "UD001",
    calculationMethod: DeductionCalculationMethod.FIXED,
    amount: 50.0,
    isSystemDefined: false,
    isActive: false,
    appliesTo: DeductionAppliesTo.NET,
    description: "Monthly union membership dues",
    createdBy: "John Doe",
    createdAt: new Date("2023-01-04T00:00:00Z"),
    updatedAt: new Date("2023-01-04T00:00:00Z"),
  },
  {
    id: "550e8400-e29b-41d4-a716-446655440004",
    businessId: "business-1",
    deductionName: "Loan Repayment",
    deductionCode: "LR001",
    calculationMethod: DeductionCalculationMethod.FIXED,
    amount: 500.0,
    isSystemDefined: false,
    isActive: true,
    appliesTo: DeductionAppliesTo.NET,
    description: "Employee loan repayment deduction",
    createdBy: "Jane Smith",
    createdAt: new Date("2023-01-05T00:00:00Z"),
    updatedAt: new Date("2023-01-05T00:00:00Z"),
  },
];

// Convert to slim format
const demoDeductionTypesSlim: DeductionTypeSlimDto[] = demoDeductionTypes.map(
  (dt) => ({
    id: dt.id,
    deductionName: dt.deductionName,
    deductionCode: dt.deductionCode,
    calculationMethod: dt.calculationMethod,
    isSystemDefined: dt.isSystemDefined,
    isActive: dt.isActive,
    appliesTo: dt.appliesTo,
  })
);

// Convert to list format
const demoDeductionTypesList: DeductionTypeListDto[] = demoDeductionTypes.map(
  (dt) => ({
    id: dt.id,
    deductionName: dt.deductionName,
    deductionCode: dt.deductionCode,
    calculationMethod: dt.calculationMethod,
    amount: dt.amount,
    isSystemDefined: dt.isSystemDefined,
    isActive: dt.isActive,
    appliesTo: dt.appliesTo,
    description: dt.description,
    createdAt: dt.createdAt,
    updatedAt: dt.updatedAt,
  })
);

// Demo API functions
export async function createDemoDeductionTypeApi(
  data: CreateDeductionTypeDto
): Promise<DeductionTypeIdResponse> {
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500));

  const newId = `demo-${Date.now()}`;

  return {
    status: ApiStatus.SUCCESS,
    message: "Deduction type created successfully",
    data: { id: newId },
  };
}

export async function bulkCreateDemoDeductionTypesApi(
  deductionTypes: CreateDeductionTypeDto[]
): Promise<BulkDeductionTypeIdsResponse> {
  await new Promise((resolve) => setTimeout(resolve, 800));

  const ids = deductionTypes.map(
    (_, index) => `demo-bulk-${Date.now()}-${index}`
  );

  return {
    status: ApiStatus.SUCCESS,
    message: `Successfully created ${ids.length} deduction types`,
    data: { ids },
  };
}

export async function getDemoDeductionTypesApi(
  params: GetDeductionTypesSchema
): Promise<DeductionTypeListPaginatedResponse> {
  await new Promise((resolve) => setTimeout(resolve, 300));

  let filteredData = [...demoDeductionTypesList];

  // Apply filters
  if (params.deductionName) {
    filteredData = filteredData.filter((dt) =>
      dt.deductionName
        .toLowerCase()
        .includes(params.deductionName!.toLowerCase())
    );
  }

  if (params.deductionCode) {
    filteredData = filteredData.filter((dt) =>
      dt.deductionCode
        .toLowerCase()
        .includes(params.deductionCode!.toLowerCase())
    );
  }

  if (params.calculationMethod) {
    filteredData = filteredData.filter(
      (dt) => dt.calculationMethod === params.calculationMethod
    );
  }

  if (params.isSystemDefined !== undefined) {
    filteredData = filteredData.filter(
      (dt) => dt.isSystemDefined === params.isSystemDefined
    );
  }

  if (params.isActive !== undefined) {
    filteredData = filteredData.filter((dt) => dt.isActive === params.isActive);
  }

  if (params.appliesTo) {
    filteredData = filteredData.filter(
      (dt) => dt.appliesTo === params.appliesTo
    );
  }

  // Apply pagination
  const page = params.page || 1;
  const limit = params.limit || 10;
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedData = filteredData.slice(startIndex, endIndex);

  return {
    status: ApiStatus.SUCCESS,
    message: "Deduction types retrieved successfully",
    data: {
      data: paginatedData,
      meta: {
        total: filteredData.length,
        page,
        totalPages: Math.ceil(filteredData.length / limit),
      },
    },
  };
}

export async function checkDemoDeductionCodeAvailabilityApi(
  deductionCode: string
): Promise<DeductionTypeCodeAvailabilityResponse> {
  await new Promise((resolve) => setTimeout(resolve, 200));

  const isAvailable = !demoDeductionTypes.some(
    (dt) => dt.deductionCode.toLowerCase() === deductionCode.toLowerCase()
  );

  return {
    status: ApiStatus.SUCCESS,
    message: "Code availability checked successfully",
    data: { available: isAvailable },
  };
}

export async function getDemoDeductionTypeApi(
  id: string
): Promise<DeductionTypeResponse> {
  await new Promise((resolve) => setTimeout(resolve, 300));

  const deductionType = demoDeductionTypes.find((dt) => dt.id === id);

  if (!deductionType) {
    return {
      status: ApiStatus.FAIL,
      message: "Deduction type not found",
      data: null,
    };
  }

  return {
    status: ApiStatus.SUCCESS,
    message: "Deduction type retrieved successfully",
    data: deductionType,
  };
}

export async function getDemoDeductionTypesSlimApi(): Promise<SimpleDeductionTypeResponse> {
  await new Promise((resolve) => setTimeout(resolve, 200));

  return {
    status: ApiStatus.SUCCESS,
    message: "Deduction types retrieved successfully",
    data: demoDeductionTypesSlim.filter((dt) => dt.isActive),
  };
}

export async function bulkDeleteDemoDeductionTypesApi(): Promise<BulkDeleteDeductionTypeResponse> {
  await new Promise((resolve) => setTimeout(resolve, 600));

  return {
    status: ApiStatus.SUCCESS,
    message: "Successfully deleted 2 deduction types",
    data: {
      deleted: 2,
      message: "Successfully deleted 2 deduction types",
      deletedIds: ["demo-1", "demo-2"],
    },
  };
}

// Update a deduction type
export async function updateDemoDeductionTypeApi(
  id: string,
  data: any
): Promise<DeductionTypeIdResponse> {
  await new Promise((resolve) => setTimeout(resolve, 400));

  return {
    status: ApiStatus.SUCCESS,
    message: "Deduction type updated successfully",
    data: { id },
  };
}

// Update deduction type status (isActive)
export async function updateDemoDeductionTypeStatusApi(
  id: string,
  isActive: boolean
): Promise<DeductionTypeIdResponse> {
  await new Promise((resolve) => setTimeout(resolve, 300));

  return {
    status: ApiStatus.SUCCESS,
    message: `Deduction type status updated to ${
      isActive ? "active" : "inactive"
    }`,
    data: { id },
  };
}

export async function bulkUpdateDemoDeductionTypeStatusApi(): Promise<BulkUpdateDeductionTypeStatusResponse> {
  await new Promise((resolve) => setTimeout(resolve, 500));

  return {
    status: ApiStatus.SUCCESS,
    message: "Successfully updated status for 3 deduction types",
    data: {
      updated: 3,
      message: "Successfully updated status for 3 deduction types",
      updatedIds: ["demo-1", "demo-2", "demo-3"],
    },
  };
}
