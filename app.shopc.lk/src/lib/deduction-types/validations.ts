import { z } from "zod";
import { DeductionCalculationMethod, DeductionAppliesTo } from "@/types/deduction-type";

// Backend DTO: CreateDeductionTypeDto
export const createDeductionTypeSchema = z.object({
  deductionName: z.string().min(1, "Deduction name is required").max(191, "Deduction name must be less than 191 characters"),
  deductionCode: z.string().min(1, "Deduction code is required").max(191, "Deduction code must be less than 191 characters"),
  calculationMethod: z.nativeEnum(DeductionCalculationMethod, {
    errorMap: () => ({ message: "Please select a valid calculation method" }),
  }),
  amount: z.number().positive("Amount must be positive").optional(),
  isSystemDefined: z.boolean().optional().default(false),
  isActive: z.boolean().optional().default(true),
  appliesTo: z.nativeEnum(DeductionAppliesTo, {
    errorMap: () => ({ message: "Please select what the deduction applies to" }),
  }).optional().default(DeductionAppliesTo.GROSS),
  description: z.string().max(500, "Description must be less than 500 characters").optional(),
});

// Backend DTO: UpdateDeductionTypeDto
export const updateDeductionTypeSchema = z.object({
  deductionName: z.string().min(1, "Deduction name is required").max(191, "Deduction name must be less than 191 characters").optional(),
  deductionCode: z.string().min(1, "Deduction code is required").max(191, "Deduction code must be less than 191 characters").optional(),
  calculationMethod: z.nativeEnum(DeductionCalculationMethod, {
    errorMap: () => ({ message: "Please select a valid calculation method" }),
  }).optional(),
  amount: z.number().positive("Amount must be positive").optional(),
  isSystemDefined: z.boolean().optional(),
  isActive: z.boolean().optional(),
  appliesTo: z.nativeEnum(DeductionAppliesTo, {
    errorMap: () => ({ message: "Please select what the deduction applies to" }),
  }).optional(),
  description: z.string().max(500, "Description must be less than 500 characters").optional(),
});

// Schema for getting deduction types with filters
export const getDeductionTypesSchema = z.object({
  page: z.number().int().min(1).optional(),
  limit: z.number().int().min(1).max(100).optional(),
  from: z.string().optional(),
  to: z.string().optional(),
  deductionName: z.string().optional(),
  deductionCode: z.string().optional(),
  calculationMethod: z.string().optional(),
  isSystemDefined: z.boolean().optional(),
  isActive: z.boolean().optional(),
  appliesTo: z.string().optional(),
  filters: z.string().optional(),
  joinOperator: z.enum(["and", "or"]).optional(),
  sort: z.string().optional(),
});

// Schema for bulk create deduction types
export const bulkCreateDeductionTypesSchema = z.object({
  deductionTypes: z.array(createDeductionTypeSchema).min(1, "At least one deduction type is required"),
});

// Schema for bulk delete deduction types
export const bulkDeleteDeductionTypesSchema = z.object({
  deductionTypeIds: z.array(z.string().uuid("Invalid deduction type ID")).min(1, "At least one deduction type ID is required"),
});

// Schema for bulk update deduction type status
export const bulkUpdateDeductionTypeStatusSchema = z.object({
  deductionTypeIds: z.array(z.string().uuid("Invalid deduction type ID")).min(1, "At least one deduction type ID is required"),
  isActive: z.boolean(),
});

// Form schema for UI components (includes additional fields for form handling)
export const deductionTypeFormSchema = z.object({
  deductionName: z.string().min(1, "Deduction name is required").max(191, "Deduction name must be less than 191 characters"),
  deductionCode: z.string().min(1, "Deduction code is required").max(191, "Deduction code must be less than 191 characters"),
  calculationMethod: z.nativeEnum(DeductionCalculationMethod, {
    errorMap: () => ({ message: "Please select a valid calculation method" }),
  }),
  amount: z.number().positive("Amount must be positive").optional(),
  isSystemDefined: z.boolean().optional().default(false),
  isActive: z.boolean().optional().default(true),
  appliesTo: z.nativeEnum(DeductionAppliesTo, {
    errorMap: () => ({ message: "Please select what the deduction applies to" }),
  }).optional().default(DeductionAppliesTo.GROSS),
  description: z.string().max(500, "Description must be less than 500 characters").optional(),
});

// Type exports for use in components
export type CreateDeductionTypeSchema = z.infer<typeof createDeductionTypeSchema>;
export type UpdateDeductionTypeSchema = z.infer<typeof updateDeductionTypeSchema>;
export type GetDeductionTypesSchema = z.infer<typeof getDeductionTypesSchema>;
export type BulkCreateDeductionTypesSchema = z.infer<typeof bulkCreateDeductionTypesSchema>;
export type BulkDeleteDeductionTypesSchema = z.infer<typeof bulkDeleteDeductionTypesSchema>;
export type BulkUpdateDeductionTypeStatusSchema = z.infer<typeof bulkUpdateDeductionTypeStatusSchema>;
export type DeductionTypeFormSchema = z.infer<typeof deductionTypeFormSchema>;
