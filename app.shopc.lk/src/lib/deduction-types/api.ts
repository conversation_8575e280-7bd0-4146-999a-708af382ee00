import {
  DeductionTypeResponse,
  DeductionTypePaginatedResponse,
  DeductionTypeListPaginatedResponse,
  DeductionTypeIdResponse,
  BulkDeductionTypeIdsResponse,
  BulkDeleteDeductionTypeDto,
  BulkDeleteDeductionTypeResponse,
  DeductionTypeCodeAvailabilityResponse,
  SimpleDeductionTypeResponse,
  CreateDeductionTypeDto,
  UpdateDeductionTypeDto,
  BulkCreateDeductionTypeDto,
  BulkUpdateDeductionTypeStatusDto,
  BulkUpdateDeductionTypeStatusResponse,
} from "@/types/deduction-type";
import { ApiResponse, ApiStatus } from "@/types/common";
import { GetDeductionTypesSchema } from "./validations";
import axios from "@/utils/axios";

// Create a new deduction type
export async function createDeductionTypeApi(
  data: CreateDeductionTypeDto
): Promise<DeductionTypeIdResponse> {
  try {
    const res = await axios.post("/deduction-types", data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk create deduction types
export async function bulkCreateDeductionTypesApi(
  deductionTypes: BulkCreateDeductionTypeDto[]
): Promise<BulkDeductionTypeIdsResponse> {
  try {
    const res = await axios.post("/deduction-types/bulk", {
      deductionTypes: JSON.stringify(deductionTypes),
    });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get all deduction types with pagination and filtering
export async function getDeductionTypesApi(
  params: GetDeductionTypesSchema
): Promise<DeductionTypeListPaginatedResponse> {
  try {
    const queryParams = new URLSearchParams();
    
    if (params.page) queryParams.append("page", params.page.toString());
    if (params.limit) queryParams.append("limit", params.limit.toString());
    if (params.from) queryParams.append("from", params.from);
    if (params.to) queryParams.append("to", params.to);
    if (params.deductionName) queryParams.append("deductionName", params.deductionName);
    if (params.deductionCode) queryParams.append("deductionCode", params.deductionCode);
    if (params.calculationMethod) queryParams.append("calculationMethod", params.calculationMethod);
    if (params.isSystemDefined !== undefined) queryParams.append("isSystemDefined", params.isSystemDefined.toString());
    if (params.isActive !== undefined) queryParams.append("isActive", params.isActive.toString());
    if (params.appliesTo) queryParams.append("appliesTo", params.appliesTo);
    if (params.filters) queryParams.append("filters", params.filters);
    if (params.joinOperator) queryParams.append("joinOperator", params.joinOperator);
    if (params.sort) queryParams.append("sort", params.sort);

    const res = await axios.get(`/deduction-types?${queryParams.toString()}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Check deduction code availability
export async function checkDeductionCodeAvailabilityApi(
  deductionCode: string
): Promise<DeductionTypeCodeAvailabilityResponse> {
  try {
    const res = await axios.get(`/deduction-types/check-availability?deductionCode=${encodeURIComponent(deductionCode)}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get deduction types in slim format (for dropdowns/selects)
export async function getDeductionTypesSlimApi(): Promise<SimpleDeductionTypeResponse> {
  try {
    const res = await axios.get("/deduction-types/slim");
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Get a single deduction type by ID
export async function getDeductionTypeApi(id: string): Promise<DeductionTypeResponse> {
  try {
    const res = await axios.get(`/deduction-types/${id}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Update a deduction type
export async function updateDeductionTypeApi(
  id: string,
  data: UpdateDeductionTypeDto
): Promise<DeductionTypeIdResponse> {
  try {
    const res = await axios.patch(`/deduction-types/${id}`, data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Delete a deduction type
export async function deleteDeductionTypeApi(id: string): Promise<ApiResponse<null>> {
  try {
    const res = await axios.delete(`/deduction-types/${id}`);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk delete deduction types
export async function bulkDeleteDeductionTypesApi(
  data: BulkDeleteDeductionTypeDto
): Promise<BulkDeleteDeductionTypeResponse> {
  try {
    const res = await axios.delete("/deduction-types/bulk", { data });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Update deduction type status (isActive)
export async function updateDeductionTypeStatusApi(
  id: string,
  isActive: boolean
): Promise<DeductionTypeIdResponse> {
  try {
    const res = await axios.patch(`/deduction-types/${id}/status`, { isActive });
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}

// Bulk update deduction type status
export async function bulkUpdateDeductionTypeStatusApi(
  data: BulkUpdateDeductionTypeStatusDto
): Promise<BulkUpdateDeductionTypeStatusResponse> {
  try {
    const res = await axios.patch("/deduction-types/bulk/status", data);
    return res.data;
  } catch (error: any) {
    return {
      status: ApiStatus.FAIL,
      message: error.response?.data?.message || "Something went wrong",
      data: null,
    };
  }
}
