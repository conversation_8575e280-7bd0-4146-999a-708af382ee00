"use client";

import * as React from "react";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { useAllowanceType } from "@/lib/allowance-types/hooks";
import { AllowanceCalculationMethod } from "@/types/allowance-type";
import { formatDate } from "@/lib/utils";

interface AllowanceTypeDetailsContentProps {
  allowanceTypeId: string;
  isDemo?: boolean;
}

export function AllowanceTypeDetailsContent({
  allowanceTypeId,
  isDemo = false,
}: AllowanceTypeDetailsContentProps) {
  const {
    data: allowanceTypeData,
    isLoading,
    error,
  } = useAllowanceType(allowanceTypeId, isDemo);

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="space-y-2">
          <Skeleton className="h-4 w-[250px]" />
          <Skeleton className="h-4 w-[200px]" />
        </div>
        <Separator />
        <div className="space-y-4">
          <Skeleton className="h-4 w-[300px]" />
          <Skeleton className="h-4 w-[250px]" />
          <Skeleton className="h-4 w-[200px]" />
        </div>
      </div>
    );
  }

  if (error || !allowanceTypeData?.data) {
    return (
      <div className="flex items-center justify-center py-8">
        <p className="text-muted-foreground">
          Failed to load allowance type details
        </p>
      </div>
    );
  }

  const allowanceType = allowanceTypeData.data;

  const calculationMethodLabels = {
    [AllowanceCalculationMethod.FIXED]: "Fixed Amount",
    [AllowanceCalculationMethod.PERCENTAGE]: "Percentage",
    [AllowanceCalculationMethod.FORMULA]: "Formula Based",
    [AllowanceCalculationMethod.SLAB]: "Slab Based",
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">
            {allowanceType.allowanceName}
          </h3>
          <Badge variant={allowanceType.isActive ? "default" : "outline"}>
            {allowanceType.isActive ? "Active" : "Inactive"}
          </Badge>
        </div>
        <p className="text-sm text-muted-foreground">
          Code: {allowanceType.allowanceCode}
        </p>
      </div>

      <Separator />

      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Basic Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Allowance Name
              </p>
              <p className="text-sm">{allowanceType.allowanceName}</p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Allowance Code
              </p>
              <p className="text-sm font-mono">{allowanceType.allowanceCode}</p>
            </div>
          </div>

          <div>
            <p className="text-sm font-medium text-muted-foreground">
              Calculation Method
            </p>
            <Badge variant="outline">
              {calculationMethodLabels[allowanceType.calculationMethod] ||
                allowanceType.calculationMethod}
            </Badge>
          </div>

          {allowanceType.amount && (
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Amount
                </p>
                <p className="text-sm font-semibold">
                  Rs. {allowanceType.amount.toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Tax Rate ID
                </p>
                <p className="text-sm font-mono">
                  {allowanceType.taxRateId || "-"}
                </p>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Taxable
              </p>
              <Badge
                variant={allowanceType.isTaxable ? "default" : "secondary"}
              >
                {allowanceType.isTaxable ? "Yes" : "No"}
              </Badge>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                EPF/ETF Eligible
              </p>
              <Badge
                variant={allowanceType.isEPFETFEligible ? "default" : "secondary"}
              >
                {allowanceType.isEPFETFEligible ? "Yes" : "No"}
              </Badge>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Status
              </p>
              <Badge variant={allowanceType.isActive ? "default" : "outline"}>
                {allowanceType.isActive ? "Active" : "Inactive"}
              </Badge>
            </div>
          </div>

          {allowanceType.description && (
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Description
              </p>
              <p className="text-sm">{allowanceType.description}</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* System Information */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">System Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Created At
              </p>
              <p className="text-sm">
                {formatDate(new Date(allowanceType.createdAt))}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Updated At
              </p>
              <p className="text-sm">
                {formatDate(new Date(allowanceType.updatedAt))}
              </p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium text-muted-foreground">
                Created By
              </p>
              <p className="text-sm">{allowanceType.createdBy}</p>
            </div>
            {allowanceType.updatedBy && (
              <div>
                <p className="text-sm font-medium text-muted-foreground">
                  Updated By
                </p>
                <p className="text-sm">{allowanceType.updatedBy}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
