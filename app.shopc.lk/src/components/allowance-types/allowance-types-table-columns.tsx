"use client";

import * as React from "react";
import type { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Eye } from "lucide-react";
import {
  AllowanceTypeTableData,
  AllowanceCalculationMethod,
} from "@/types/allowance-type";
import { AllowanceTypeStatusBadge } from "./allowance-type-status-badge";
import { Badge } from "@/components/ui/badge";

export interface ColumnsProps {
  setRowAction: (rowAction: {
    type: "view" | "update" | "delete";
    row: any;
  }) => void;
  isDemo?: boolean;
  isActionsDisabled?: boolean;
  onStatusUpdate?: (allowanceTypeId: string, newStatus: boolean) => void;
}

export function getColumns({
  setRowAction,
  isDemo = false,
  isActionsDisabled = false,
  onStatusUpdate,
}: ColumnsProps) {
  const columns: ColumnDef<AllowanceTypeTableData>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-[2px]"
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="translate-y-[2px]"
        />
      ),
      enableSorting: false,
      enableHiding: false,
    },
    {
      accessorKey: "allowanceName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Allowance Name" />
      ),
      cell: ({ row }) => {
        return (
          <div className="flex space-x-2">
            <span className="max-w-[500px] truncate font-medium">
              {row.getValue("allowanceName")}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "allowanceCode",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Code" />
      ),
      cell: ({ row }) => {
        return (
          <div className="flex space-x-2">
            <span className="max-w-[200px] truncate font-mono text-sm">
              {row.getValue("allowanceCode")}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "calculationMethod",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Calculation Method" />
      ),
      cell: ({ row }) => {
        const method = row.getValue(
          "calculationMethod"
        ) as AllowanceCalculationMethod;
        const methodLabels = {
          [AllowanceCalculationMethod.FIXED]: "Fixed",
          [AllowanceCalculationMethod.PERCENTAGE]: "Percentage",
          [AllowanceCalculationMethod.FORMULA]: "Formula",
          [AllowanceCalculationMethod.SLAB]: "Slab",
        };

        return (
          <Badge variant="outline">{methodLabels[method] || method}</Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "amount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Amount" />
      ),
      cell: ({ row }) => {
        const amount = row.getValue("amount") as number;
        return (
          <div className="flex space-x-2">
            <span className="max-w-[150px] truncate">
              {amount ? `Rs. ${amount.toLocaleString()}` : "-"}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "isTaxable",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Taxable" />
      ),
      cell: ({ row }) => {
        const isTaxable = row.getValue("isTaxable") as boolean;
        return (
          <Badge variant={isTaxable ? "default" : "secondary"}>
            {isTaxable ? "Yes" : "No"}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "isEPFETFEligible",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="EPF/ETF Eligible" />
      ),
      cell: ({ row }) => {
        const isEPFETFEligible = row.getValue("isEPFETFEligible") as boolean;
        return (
          <Badge variant={isEPFETFEligible ? "default" : "secondary"}>
            {isEPFETFEligible ? "Yes" : "No"}
          </Badge>
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "isActive",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Status" />
      ),
      cell: ({ row }) => {
        const isActive = row.getValue("isActive") as boolean;
        return (
          <AllowanceTypeStatusBadge
            allowanceTypeId={row.original.id}
            isActive={isActive}
            isDemo={isDemo}
            onStatusUpdate={onStatusUpdate}
          />
        );
      },
      filterFn: (row, id, value) => {
        return value.includes(row.getValue(id));
      },
    },
    {
      accessorKey: "createdAt",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Created At" />
      ),
      cell: ({ row }) => {
        const date = new Date(row.getValue("createdAt"));
        return (
          <div className="flex space-x-2">
            <span className="max-w-[200px] truncate">
              {date.toLocaleDateString()}
            </span>
          </div>
        );
      },
    },
    {
      id: "actions",
      cell: ({ row }) => (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="ghost"
              className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
              disabled={isActionsDisabled}
            >
              <MoreHorizontal className="h-4 w-4" />
              <span className="sr-only">Open menu</span>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-[160px]">
            <DropdownMenuItem
              onClick={() => setRowAction({ type: "view", row })}
            >
              <Eye className="mr-2 h-3.5 w-3.5 text-muted-foreground/70" />
              View
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => setRowAction({ type: "update", row })}
            >
              Edit
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => setRowAction({ type: "delete", row })}
            >
              Delete
              <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      ),
    },
  ];

  return columns;
}
