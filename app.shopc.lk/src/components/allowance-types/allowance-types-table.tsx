"use client";

import * as React from "react";
import type {
  DataTableAdvancedFilterField,
  DataTableFilterField,
  DataTableRowAction,
} from "@/types";
import { BaseTable } from "@/components/shared/base-table";
import { DeleteAllowanceTypesDialog } from "./delete-allowance-types-dialog";
import { AllowanceTypesTableToolbarActions } from "./allowance-types-table-toolbar-actions";
import { AllowanceTypeSheet } from "./allowance-type-sheet";
import { useSearchParams } from "next/navigation";
import { getColumns } from "./allowance-types-table-columns";
import {
  AllowanceTypeTableData,
  AllowanceCalculationMethod,
} from "@/types/allowance-type";
import {
  useAllowanceTypesData,
  useBulkUpdateAllowanceTypeStatus,
} from "@/lib/allowance-types/hooks";
import { useQueryClient } from "@tanstack/react-query";
import { allowanceTypeKeys } from "@/lib/allowance-types/hooks";
import { toast } from "sonner";

interface AllowanceTypesTableProps {
  isDemo?: boolean;
}

export function AllowanceTypesTable({
  isDemo = false,
}: AllowanceTypesTableProps) {
  const [rowAction, setRowAction] =
    React.useState<DataTableRowAction<AllowanceTypeTableData> | null>(null);
  const searchParams = useSearchParams();
  const queryClient = useQueryClient();

  // Get search params for table state
  const searchParamsValues = React.useMemo(() => {
    const page = Number(searchParams.get("page")) || 1;
    const perPage = Number(searchParams.get("perPage")) || 10;

    return {
      page,
      perPage,
      sort: [],
      filters: [],
      joinOperator: "and" as const,
      from: "",
      to: "",
      operator: "iLike" as const,
    };
  }, [searchParams]);

  // Fetch data using the hook
  const { data: allowanceTypesData, isLoading } = useAllowanceTypesData(
    searchParamsValues,
    isDemo
  );

  const filterFields: DataTableFilterField<AllowanceTypeTableData>[] =
    React.useMemo(
      () => [
        {
          id: "allowanceName",
          label: "Name",
          placeholder: "Filter by name...",
        },
        {
          id: "allowanceCode",
          label: "Code",
          placeholder: "Filter by code...",
        },
        {
          id: "calculationMethod",
          label: "Calculation Method",
          placeholder: "Filter by calculation method...",
          type: "select",
          options: [
            { label: "Fixed", value: AllowanceCalculationMethod.FIXED },
            {
              label: "Percentage",
              value: AllowanceCalculationMethod.PERCENTAGE,
            },
            { label: "Formula", value: AllowanceCalculationMethod.FORMULA },
            { label: "Slab", value: AllowanceCalculationMethod.SLAB },
          ],
        },
        {
          id: "isTaxable",
          label: "Taxable",
          placeholder: "Filter by taxable status...",
          type: "select",
          options: [
            { label: "Yes", value: "true" },
            { label: "No", value: "false" },
          ],
        },
        {
          id: "isEPFETFEligible",
          label: "EPF/ETF Eligible",
          placeholder: "Filter by EPF/ETF eligibility...",
          type: "select",
          options: [
            { label: "Yes", value: "true" },
            { label: "No", value: "false" },
          ],
        },
        {
          id: "isActive",
          label: "Status",
          placeholder: "Filter by status...",
          type: "select",
          options: [
            { label: "Active", value: "true" },
            { label: "Inactive", value: "false" },
          ],
        },
      ],
      []
    );

  const advancedFilterFields: DataTableAdvancedFilterField<AllowanceTypeTableData>[] =
    React.useMemo(
      () => [
        {
          id: "allowanceName",
          label: "Allowance Name",
          type: "text",
        },
        {
          id: "allowanceCode",
          label: "Allowance Code",
          type: "text",
        },
        {
          id: "calculationMethod",
          label: "Calculation Method",
          type: "select",
          options: [
            { label: "Fixed", value: AllowanceCalculationMethod.FIXED },
            {
              label: "Percentage",
              value: AllowanceCalculationMethod.PERCENTAGE,
            },
            { label: "Formula", value: AllowanceCalculationMethod.FORMULA },
            { label: "Slab", value: AllowanceCalculationMethod.SLAB },
          ],
        },
        {
          id: "isTaxable",
          label: "Taxable",
          type: "boolean",
        },
        {
          id: "isEPFETFEligible",
          label: "EPF/ETF Eligible",
          type: "boolean",
        },
        {
          id: "isActive",
          label: "Status",
          type: "boolean",
        },
        {
          id: "createdAt",
          label: "Created At",
          type: "date",
        },
        {
          id: "updatedAt",
          label: "Updated At",
          type: "date",
        },
      ],
      []
    );

  // State for dialogs and sheets
  const [showDeleteDialog, setShowDeleteDialog] = React.useState(false);
  const [showAllowanceTypeSheet, setShowAllowanceTypeSheet] =
    React.useState(false);

  // Mutations
  const bulkUpdateStatus = useBulkUpdateAllowanceTypeStatus(isDemo);

  // Handle bulk operations
  const handleBulkStatusUpdate = React.useCallback(
    (allowanceTypeIds: string[], isActive: boolean) => {
      bulkUpdateStatus.mutate(
        { allowanceTypeIds, isActive },
        {
          onSuccess: (data) => {
            toast.success(
              `${
                data.data?.updated || allowanceTypeIds.length
              } allowance types ${
                isActive ? "activated" : "deactivated"
              } successfully`
            );
          },
          onError: (error: any) => {
            toast.error(
              error?.message ||
                `Failed to ${
                  isActive ? "activate" : "deactivate"
                } allowance types`
            );
          },
        }
      );
    },
    [bulkUpdateStatus]
  );

  // Handle row actions
  React.useEffect(() => {
    if (rowAction?.type === "delete") {
      setShowDeleteDialog(true);
    } else if (rowAction?.type === "update") {
      setShowAllowanceTypeSheet(true);
    }
  }, [rowAction]);

  const columns = React.useMemo(
    () =>
      getColumns({
        setRowAction,
        isDemo,
        isActionsDisabled: false,
        onStatusUpdate: (allowanceTypeId: string, newStatus: boolean) => {
          handleBulkStatusUpdate([allowanceTypeId], newStatus);
        },
      }),
    [setRowAction, isDemo, handleBulkStatusUpdate]
  );

  const handleRefresh = async () => {
    queryClient.invalidateQueries({
      queryKey: allowanceTypeKeys.list(),
    });
  };

  return (
    <>
      <BaseTable<AllowanceTypeTableData, typeof allowanceTypesData>
        data={allowanceTypesData || undefined}
        isLoading={isLoading}
        isDemo={isDemo}
        columns={columns}
        filterFields={filterFields}
        advancedFilterFields={advancedFilterFields}
        getInitialData={(response) => {
          return response?.data?.data ?? [];
        }}
        getPageCount={(response) => {
          return response?.data?.meta?.totalPages ?? 1;
        }}
        getRowId={(row) => row.id}
        enableAdvancedTable={true}
        enableFloatingBar={false}
        ToolbarActions={AllowanceTypesTableToolbarActions}
        onRowAction={(action: DataTableRowAction<AllowanceTypeTableData>) => {
          setRowAction(action);
        }}
        onRefresh={handleRefresh}
      />

      <DeleteAllowanceTypesDialog
        open={showDeleteDialog}
        onOpenChange={setShowDeleteDialog}
        allowanceTypes={rowAction?.type === "delete" ? [rowAction.row] : []}
        showTrigger={false}
        onSuccess={() => setRowAction(null)}
        isDemo={isDemo}
      />

      <AllowanceTypeSheet
        open={showAllowanceTypeSheet}
        onOpenChange={setShowAllowanceTypeSheet}
        allowanceType={
          rowAction?.type === "update" ? rowAction.row.original : null
        }
        onSuccess={() => {
          setRowAction(null);
          setShowAllowanceTypeSheet(false);
        }}
        isDemo={isDemo}
      />
    </>
  );
}
