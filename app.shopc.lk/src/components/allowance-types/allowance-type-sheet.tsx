"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { Info, Check, X, Loader2 } from "lucide-react";
import { type z } from "zod";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import {
  createAllowanceTypeSchema,
  updateAllowanceTypeSchema,
} from "@/lib/allowance-types/validations";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  AllowanceTypeTableData,
  AllowanceCalculationMethod,
  UpdateAllowanceTypeDto,
} from "@/types/allowance-type";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  useAllowanceType,
  useAllowanceTypeCodeAvailability,
  useCreateAllowanceType,
  useUpdateAllowanceType,
} from "@/lib/allowance-types/hooks";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";

type AllowanceTypeFormValues = z.infer<typeof createAllowanceTypeSchema>;

interface AllowanceTypeSheetProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  allowanceType?: AllowanceTypeTableData | null;
  onSuccess?: () => void;
  isDemo?: boolean;
}

export function AllowanceTypeSheet({
  open,
  onOpenChange,
  allowanceType,
  onSuccess,
  isDemo = false,
}: AllowanceTypeSheetProps) {
  const isEditing = !!allowanceType;
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const formRef = React.useRef<HTMLFormElement>(
    null!
  ) as React.RefObject<HTMLFormElement>;

  // Form setup
  const form = useForm<AllowanceTypeFormValues>({
    resolver: zodResolver(
      isEditing ? updateAllowanceTypeSchema : createAllowanceTypeSchema
    ),
    defaultValues: {
      allowanceName: "",
      allowanceCode: "",
      calculationMethod: AllowanceCalculationMethod.FIXED,
      isTaxable: true,
      taxRateId: "",
      amount: undefined,
      isActive: true,
      isEPFETFEligible: false,
      description: "",
    },
  });

  // Hooks
  const createAllowanceType = useCreateAllowanceType(isDemo);
  const updateAllowanceType = useUpdateAllowanceType(isDemo);
  const { data: allowanceTypeData } = useAllowanceType(
    allowanceType?.id || "",
    isDemo
  );

  // Watch form values for code availability check
  const watchedCode = form.watch("allowanceCode");
  const { data: codeAvailability } = useAllowanceTypeCodeAvailability(
    watchedCode,
    isEditing ? allowanceType?.id : undefined,
    isDemo
  );

  // Reset form when allowance type data changes
  React.useEffect(() => {
    if (isEditing && allowanceTypeData?.data) {
      const data = allowanceTypeData.data;
      form.reset({
        allowanceName: data.allowanceName,
        allowanceCode: data.allowanceCode,
        calculationMethod: data.calculationMethod,
        isTaxable: data.isTaxable,
        taxRateId: data.taxRateId || "",
        amount: data.amount,
        isActive: data.isActive,
        isEPFETFEligible: data.isEPFETFEligible,
        description: data.description || "",
      });
    } else if (!isEditing) {
      form.reset({
        allowanceName: "",
        allowanceCode: "",
        calculationMethod: AllowanceCalculationMethod.FIXED,
        isTaxable: true,
        taxRateId: "",
        amount: undefined,
        isActive: true,
        isEPFETFEligible: false,
        description: "",
      });
    }
  }, [isEditing, allowanceTypeData, form]);

  // Handle form submission
  const handleSubmit = async () => {
    const values = form.getValues();

    setIsSubmitting(true);
    try {
      if (isEditing && allowanceType) {
        const updateData: UpdateAllowanceTypeDto = {
          allowanceName: values.allowanceName,
          allowanceCode: values.allowanceCode,
          calculationMethod: values.calculationMethod,
          isTaxable: values.isTaxable,
          taxRateId: values.taxRateId,
          amount: values.amount,
          isActive: values.isActive,
          isEPFETFEligible: values.isEPFETFEligible,
          description: values.description,
        };

        await updateAllowanceType.mutateAsync({
          id: allowanceType.id,
          data: updateData,
        });

        toast.success("Allowance type updated successfully");
      } else {
        await createAllowanceType.mutateAsync(values);
        toast.success("Allowance type created successfully");
      }

      onSuccess?.();
      onOpenChange(false);
    } catch (error: any) {
      toast.error(error?.message || "Something went wrong");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Check if code is available - fix the boolean type issue
  const isCodeAvailable = codeAvailability?.data?.available !== false;
  const showCodeError = Boolean(watchedCode && !isCodeAvailable);

  // Check if sections have errors
  const {
    formState: { errors },
  } = form;

  const hasBasicInfoErrors = !!(
    errors.allowanceName ||
    errors.allowanceCode ||
    errors.calculationMethod ||
    errors.taxRateId ||
    errors.amount ||
    errors.isTaxable ||
    errors.isActive ||
    errors.description ||
    showCodeError
  );

  // Define sections for BaseSheet
  const sections = [
    {
      id: "basic-info",
      title: "Basic Information",
      icon: <Info className="h-4 w-4" />,
      hasErrors: hasBasicInfoErrors,
      content: (
        <>
          <AccordionTrigger className="px-4 py-3 hover:no-underline">
            <div className="flex items-center gap-2">
              <Info className="h-4 w-4" />
              <span className="font-medium">Basic Information</span>
            </div>
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4">
            <Form {...form}>
              <div className="grid gap-4">
                <FormField
                  control={form.control}
                  name="allowanceName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Allowance Name *</FormLabel>
                      <FormControl>
                        <Input placeholder="Enter allowance name" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="allowanceCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Allowance Code *</FormLabel>
                      <FormControl>
                        <div className="relative">
                          <Input
                            placeholder="Enter allowance code"
                            {...field}
                            className={cn(
                              showCodeError &&
                                "border-red-500 focus-visible:ring-red-500"
                            )}
                          />
                          {watchedCode && (
                            <div className="absolute right-2 top-1/2 -translate-y-1/2">
                              {isCodeAvailable ? (
                                <Check className="h-4 w-4 text-green-500" />
                              ) : (
                                <X className="h-4 w-4 text-red-500" />
                              )}
                            </div>
                          )}
                        </div>
                      </FormControl>
                      {showCodeError && (
                        <p className="text-sm text-red-500">
                          This code is already in use
                        </p>
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="calculationMethod"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Calculation Method *</FormLabel>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select calculation method" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value={AllowanceCalculationMethod.FIXED}>
                            Fixed Amount
                          </SelectItem>
                          <SelectItem
                            value={AllowanceCalculationMethod.PERCENTAGE}
                          >
                            Percentage
                          </SelectItem>
                          <SelectItem
                            value={AllowanceCalculationMethod.FORMULA}
                          >
                            Formula Based
                          </SelectItem>
                          <SelectItem value={AllowanceCalculationMethod.SLAB}>
                            Slab Based
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )}
                />


                <FormField
                  control={form.control}
                  name="taxRateId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Tax Rate ID</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Enter tax rate ID (optional)"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        UUID of the tax rate to apply
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="amount"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Amount</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          step="0.01"
                          placeholder="Enter amount (optional)"
                          value={field.value || ""}
                          onChange={(e) =>
                            field.onChange(
                              e.target.value
                                ? Number(e.target.value)
                                : undefined
                            )
                          }
                        />
                      </FormControl>
                      <FormDescription>
                        Default amount for this allowance type
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                  <FormField
                    control={form.control}
                    name="isTaxable"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Taxable</FormLabel>
                          <FormDescription>
                            Is this allowance taxable?
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="isActive"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>Active</FormLabel>
                          <FormDescription>
                            Is this allowance type active?
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="isEPFETFEligible"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                        <div className="space-y-0.5">
                          <FormLabel>EPF/ETF Eligible</FormLabel>
                          <FormDescription>
                            Is this allowance eligible for EPF/ETF?
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter description (optional)"
                          className="resize-none"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </Form>
          </AccordionContent>
        </>
      ),
    },
  ];

  return (
    <BaseSheet<AllowanceTypeTableData, AllowanceTypeFormValues>
      isDemo={isDemo}
      onSuccess={onSuccess}
      data={allowanceType}
      open={open}
      onOpenChange={onOpenChange}
      isUpdate={isEditing}
      title="Allowance Type"
      sections={sections}
      onSubmit={handleSubmit}
      formRef={formRef}
      isSubmitting={isSubmitting}
      isPending={false}
    />
  );
}
