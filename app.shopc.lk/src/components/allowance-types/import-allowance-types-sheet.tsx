"use client";

import { ImportDataSheet, FieldConfig } from "../data-import/import-data-sheet";

import { AllowanceCalculationMethod } from "@/types/allowance-type";
import { ApiStatus } from "@/types/common";
import { useBulkCreateAllowanceTypes } from "@/lib/allowance-types/hooks";

// Define the allowance type field types that can be imported
export type AllowanceTypeImportFields =
  | "allowanceName"
  | "allowanceCode"
  | "calculationMethod"
  | "isTaxable"
  | "isEPFETFEligible"
  | "taxRateId"
  | "amount"
  | "description";

// All possible fields for allowance type import
const ALL_ALLOWANCE_TYPE_FIELDS: AllowanceTypeImportFields[] = [
  "allowanceName",
  "allowanceCode",
  "calculationMethod",
  "isTaxable",
  "isEPFETFEligible",
  "taxRateId",
  "amount",
  "description",
];

export type ImportAllowanceTypesSheetProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess: () => void;
  isDemo?: boolean;
};

export function ImportAllowanceTypesSheet({
  open,
  onOpenChange,
  onSuccess,
  isDemo = false,
}: ImportAllowanceTypesSheetProps) {
  // Hook for bulk importing allowance types
  const bulkImportMutation = useBulkCreateAllowanceTypes(isDemo);

  // Field configurations for allowance types
  const ALLOWANCE_TYPE_FIELD_CONFIGS: FieldConfig[] = [
    {
      name: "allowanceName",
      type: "text",
      defaultValue: "",
      placeholder: "Enter allowance name (e.g., Basic Allowance, Transport)",
    },
    {
      name: "allowanceCode",
      type: "text",
      defaultValue: "",
      placeholder: "Enter allowance code (e.g., BASIC_001, TRANS_001)",
    },
    {
      name: "calculationMethod",
      type: "select",
      options: [
        { value: AllowanceCalculationMethod.FIXED, label: "Fixed Amount" },
        { value: AllowanceCalculationMethod.PERCENTAGE, label: "Percentage" },
        { value: AllowanceCalculationMethod.FORMULA, label: "Formula" },
        { value: AllowanceCalculationMethod.SLAB, label: "Slab" },
      ],
      defaultValue: AllowanceCalculationMethod.FIXED,
    },
    {
      name: "isTaxable",
      type: "select",
      options: [
        { value: "true", label: "Yes" },
        { value: "false", label: "No" },
      ],
      defaultValue: "true",
    },
    {
      name: "isEPFETFEligible",
      type: "select",
      options: [
        { value: "true", label: "Yes" },
        { value: "false", label: "No" },
      ],
      defaultValue: "false",
    },
    {
      name: "taxRateId",
      type: "text",
      defaultValue: "",
      placeholder: "Enter tax rate ID (optional UUID)",
    },
    {
      name: "amount",
      type: "text",
      defaultValue: "",
      placeholder: "Enter amount (e.g., 50000.00)",
    },
    {
      name: "description",
      type: "text",
      defaultValue: "",
      placeholder: "Enter description (optional)",
    },
  ];

  // Enhanced validation function for allowance types
  const validateAllowanceTypeRow = (
    row: Record<string, any>,
    rowIndex?: number,
    allData?: Record<string, any>[]
  ) => {
    const errors: Record<string, string> = {};
    let valid = true;

    // Validate required fields
    if (
      !row.allowanceName ||
      typeof row.allowanceName !== "string" ||
      row.allowanceName.trim() === ""
    ) {
      errors.allowanceName = "Allowance name is required";
      valid = false;
    }

    if (
      !row.allowanceCode ||
      typeof row.allowanceCode !== "string" ||
      row.allowanceCode.trim() === ""
    ) {
      errors.allowanceCode = "Allowance code is required";
      valid = false;
    } else {
      // Check uniqueness within dataset
      if (allData && rowIndex !== undefined) {
        const duplicateIndex = allData.findIndex(
          (item, index) =>
            index !== rowIndex &&
            item.allowanceCode &&
            item.allowanceCode.trim().toUpperCase() ===
              row.allowanceCode.trim().toUpperCase()
        );
        if (duplicateIndex !== -1) {
          errors.allowanceCode =
            "Allowance code must be unique within the import data";
          valid = false;
        }
      }
    }

    // Validate calculationMethod
    if (
      row.calculationMethod &&
      !Object.values(AllowanceCalculationMethod).includes(row.calculationMethod)
    ) {
      errors.calculationMethod = "Invalid calculation method";
      valid = false;
    }


    // Validate isTaxable if present
    if (row.isTaxable !== undefined) {
      if (typeof row.isTaxable === "string") {
        const normalizedValue = row.isTaxable.toLowerCase().trim();
        if (
          normalizedValue !== "true" &&
          normalizedValue !== "false" &&
          normalizedValue !== "yes" &&
          normalizedValue !== "no" &&
          normalizedValue !== "1" &&
          normalizedValue !== "0"
        ) {
          errors.isTaxable = "Must be true/false, yes/no, or 1/0";
          valid = false;
        }
      } else if (typeof row.isTaxable !== "boolean") {
        errors.isTaxable = "Must be a boolean value";
        valid = false;
      }
    }

    // Validate isEPFETFEligible if present
    if (row.isEPFETFEligible !== undefined) {
      if (typeof row.isEPFETFEligible === "string") {
        const normalizedValue = row.isEPFETFEligible.toLowerCase().trim();
        if (
          normalizedValue !== "true" &&
          normalizedValue !== "false" &&
          normalizedValue !== "yes" &&
          normalizedValue !== "no" &&
          normalizedValue !== "1" &&
          normalizedValue !== "0"
        ) {
          errors.isEPFETFEligible = "Must be true/false, yes/no, or 1/0";
          valid = false;
        }
      } else if (typeof row.isEPFETFEligible !== "boolean") {
        errors.isEPFETFEligible = "Must be a boolean value";
        valid = false;
      }
    }

    // Validate taxRateId if present (should be UUID format)
    if (row.taxRateId && typeof row.taxRateId === "string" && row.taxRateId.trim() !== "") {
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (!uuidRegex.test(row.taxRateId.trim())) {
        errors.taxRateId = "Tax Rate ID must be a valid UUID format";
        valid = false;
      }
    }

    // Validate amount if present
    if (row.amount !== undefined && row.amount !== null && row.amount !== "") {
      const amount = typeof row.amount === "string" ? parseFloat(row.amount) : row.amount;
      if (isNaN(amount) || amount < 0) {
        errors.amount = "Amount must be a non-negative number";
        valid = false;
      }
    }

    return { valid, errors };
  };

  // Handle submission of allowance type data
  const handleSubmitAllowanceTypes = async (data: any[]) => {
    try {
      // Transform data to BulkCreateAllowanceTypeDto format
      const allowanceTypesData = data.map((row) => {
        // Handle isTaxable - could be boolean or string
        let isTaxable = true; // Default value
        if (row.isTaxable !== undefined) {
          if (typeof row.isTaxable === "boolean") {
            isTaxable = row.isTaxable;
          } else if (typeof row.isTaxable === "string") {
            const normalizedValue = row.isTaxable.toLowerCase().trim();
            isTaxable =
              normalizedValue === "true" ||
              normalizedValue === "yes" ||
              normalizedValue === "1";
          }
        }

        // Handle isEPFETFEligible - could be boolean or string
        let isEPFETFEligible = false; // Default value
        if (row.isEPFETFEligible !== undefined) {
          if (typeof row.isEPFETFEligible === "boolean") {
            isEPFETFEligible = row.isEPFETFEligible;
          } else if (typeof row.isEPFETFEligible === "string") {
            const normalizedValue = row.isEPFETFEligible.toLowerCase().trim();
            isEPFETFEligible =
              normalizedValue === "true" ||
              normalizedValue === "yes" ||
              normalizedValue === "1";
          }
        }

        // Handle calculationMethod
        let calculationMethod = AllowanceCalculationMethod.FIXED;
        if (
          row.calculationMethod &&
          Object.values(AllowanceCalculationMethod).includes(
            row.calculationMethod
          )
        ) {
          calculationMethod =
            row.calculationMethod as AllowanceCalculationMethod;
        }

        return {
          allowanceName: row.allowanceName.trim(),
          allowanceCode: row.allowanceCode.trim(),
          calculationMethod,
          isTaxable,
          isEPFETFEligible,
          taxRateId: row.taxRateId?.trim() || undefined,
          amount: row.amount ? parseFloat(row.amount) : undefined,
          description: row.description?.trim() || "",
        };
      });

      // Use the mutation hook to import allowance types
      const result = await bulkImportMutation.mutateAsync(allowanceTypesData);

      if (result.status === ApiStatus.SUCCESS) {
        onSuccess();
        return Promise.resolve();
      } else {
        return Promise.reject(
          new Error(result.message || "Failed to import allowance types")
        );
      }
    } catch (error) {
      console.error("Error importing allowance types:", error);
      return Promise.reject(error);
    }
  };

  return (
    <ImportDataSheet
      open={open}
      onOpenChange={onOpenChange}
      title="Import Allowance Types"
      description="Import allowance types from a CSV or Excel file. Make sure your file includes the required fields."
      targetFields={ALL_ALLOWANCE_TYPE_FIELDS}
      fieldConfigs={ALLOWANCE_TYPE_FIELD_CONFIGS}
      validateRow={validateAllowanceTypeRow}
      onSubmit={handleSubmitAllowanceTypes}
      requireAllFields={false}
      defaultRowCount={1}
    />
  );
}
