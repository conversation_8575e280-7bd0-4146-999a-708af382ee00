"use client";

import * as React from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import {
  Info,
  Check,
  X,
  Loader2,
} from "lucide-react";
import { type z } from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";
import { deductionTypeFormSchema } from "@/lib/deduction-types/validations";
import { ApiStatus } from "@/types/common";
import { BaseSheet } from "@/components/shared/base-sheet";
import {
  DeductionTypeTableData,
  DeductionCalculationMethod,
  DeductionAppliesTo,
  UpdateDeductionTypeDto,
} from "@/types/deduction-type";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  useDeductionTypeData,
  useDeductionTypeCodeAvailability,
  useCreateDeductionType,
  useUpdateDeductionType,
} from "@/lib/deduction-types/hooks";
import { useEffect } from "react";

interface DeductionTypeSheetProps {
  deductionType: DeductionTypeTableData | null;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  onSuccess?: (deductionType?: DeductionTypeTableData) => void;
  isUpdate?: boolean;
  isDemo?: boolean;
}

type FormData = z.infer<typeof deductionTypeFormSchema>;

export function DeductionTypeSheet({
  deductionType,
  open,
  onOpenChange,
  onSuccess,
  isUpdate = false,
  isDemo = false,
}: DeductionTypeSheetProps) {
  const formRef = React.useRef<HTMLFormElement>(
    null
  ) as React.RefObject<HTMLFormElement>;
  const [isSubmitting, setIsSubmitting] = React.useState(false);

  // Debounced values for availability checking
  const [debouncedCode, setDebouncedCode] = React.useState<string>("");

  // Fetch complete deduction type data if updating
  const { data: fullDeductionTypeResponse, isLoading: isLoadingDeductionType } =
    useDeductionTypeData(deductionType?.id || "", isDemo);
  const fullDeductionType = fullDeductionTypeResponse?.data;

  useEffect(() => {
    if (fullDeductionTypeResponse) {
      console.log("fullDeductionTypeResponse", fullDeductionTypeResponse);
    }
  }, [fullDeductionTypeResponse]);

  // Mutation hooks for create and update operations
  const createDeductionTypeMutation = useCreateDeductionType(isDemo);
  const updateDeductionTypeMutation = useUpdateDeductionType(isDemo);

  // Availability checks (only check if not updating the same deduction type)
  const shouldCheckCodeAvailability =
    debouncedCode.length > 0 &&
    (!isUpdate ||
      (deductionType &&
        debouncedCode.toUpperCase() !== deductionType.deductionCode?.toUpperCase()));

  const { data: codeAvailabilityResponse, isLoading: isCheckingCode } =
    useDeductionTypeCodeAvailability(debouncedCode, isDemo);

  const isCodeAvailable = shouldCheckCodeAvailability
    ? codeAvailabilityResponse?.data?.available ?? true
    : true;

  const form = useForm<FormData>({
    resolver: zodResolver(deductionTypeFormSchema),
    defaultValues: {
      deductionName: "",
      deductionCode: "",
      calculationMethod: DeductionCalculationMethod.FIXED,
      amount: undefined,
      isSystemDefined: false,
      isActive: true,
      appliesTo: DeductionAppliesTo.GROSS,
      description: "",
    },
  });

  // Watch form values for debounced availability checks
  const watchedCode = form.watch("deductionCode");

  // Debounce the watched values
  React.useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedCode(watchedCode || "");
    }, 500);

    return () => clearTimeout(timer);
  }, [watchedCode]);

  // Reset form when deduction type changes or dialog opens/closes
  React.useEffect(() => {
    if (open) {
      if (isUpdate && fullDeductionType) {
        // Populate form with existing data
        form.reset({
          deductionName: fullDeductionType.deductionName || "",
          deductionCode: fullDeductionType.deductionCode || "",
          calculationMethod: fullDeductionType.calculationMethod || DeductionCalculationMethod.FIXED,
          amount: fullDeductionType.amount,
          isSystemDefined: fullDeductionType.isSystemDefined ?? false,
          isActive: fullDeductionType.isActive ?? true,
          appliesTo: fullDeductionType.appliesTo || DeductionAppliesTo.GROSS,
          description: fullDeductionType.description || "",
        });
      } else if (!isUpdate) {
        // Reset to default values for create
        form.reset({
          deductionName: "",
          deductionCode: "",
          calculationMethod: DeductionCalculationMethod.FIXED,
          amount: undefined,
          isSystemDefined: false,
          isActive: true,
          appliesTo: DeductionAppliesTo.GROSS,
          description: "",
        });
      }
    }
  }, [open, isUpdate, fullDeductionType, form]);

  const onSubmit = async (data: FormData) => {
    if (isSubmitting) return;

    // Check availability before submitting
    if (!isCodeAvailable) {
      toast.error("Deduction code is already taken");
      return;
    }

    setIsSubmitting(true);

    try {
      if (isUpdate && deductionType) {
        // Update existing deduction type
        const updateData: UpdateDeductionTypeDto = {
          deductionName: data.deductionName,
          deductionCode: data.deductionCode,
          calculationMethod: data.calculationMethod,
          amount: data.amount,
          isSystemDefined: data.isSystemDefined,
          isActive: data.isActive,
          appliesTo: data.appliesTo,
          description: data.description,
        };

        const result = await updateDeductionTypeMutation.mutateAsync({
          id: deductionType.id,
          data: updateData,
        });

        if (result.status === ApiStatus.SUCCESS) {
          toast.success("Deduction type updated successfully");
          onSuccess?.(deductionType);
          onOpenChange?.(false);
        } else {
          toast.error(result.message || "Failed to update deduction type");
        }
      } else {
        // Create new deduction type
        const result = await createDeductionTypeMutation.mutateAsync(data);

        if (result.status === ApiStatus.SUCCESS) {
          toast.success("Deduction type created successfully");
          onSuccess?.();
          onOpenChange?.(false);
        } else {
          toast.error(result.message || "Failed to create deduction type");
        }
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("An unexpected error occurred");
    } finally {
      setIsSubmitting(false);
    }
  };

  const isLoading = isLoadingDeductionType && isUpdate;

  return (
    <BaseSheet
      open={open}
      onOpenChange={onOpenChange}
      title={isUpdate ? "Edit Deduction Type" : "Create Deduction Type"}
      description={
        isUpdate
          ? "Update the deduction type information below."
          : "Fill in the information below to create a new deduction type."
      }
      className="sm:max-w-[600px]"
      isLoading={isLoading}
      onSubmit={() => formRef.current?.requestSubmit()}
      submitLabel={isUpdate ? "Update Deduction Type" : "Create Deduction Type"}
      isSubmitting={isSubmitting}
    >
      <form
        ref={formRef}
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-6"
      >
        {/* Basic Information */}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="deductionName">
              Deduction Name <span className="text-red-500">*</span>
            </Label>
            <Input
              id="deductionName"
              placeholder="Enter deduction name"
              {...form.register("deductionName")}
              className={cn(
                form.formState.errors.deductionName && "border-red-500"
              )}
            />
            {form.formState.errors.deductionName && (
              <p className="text-sm text-red-500">
                {form.formState.errors.deductionName.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="deductionCode">
              Deduction Code <span className="text-red-500">*</span>
            </Label>
            <div className="relative">
              <Input
                id="deductionCode"
                placeholder="Enter deduction code"
                {...form.register("deductionCode")}
                className={cn(
                  form.formState.errors.deductionCode && "border-red-500",
                  !isCodeAvailable && "border-red-500"
                )}
              />
              {isCheckingCode && (
                <div className="absolute right-3 top-1/2 -translate-y-1/2">
                  <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
                </div>
              )}
              {!isCheckingCode && shouldCheckCodeAvailability && (
                <div className="absolute right-3 top-1/2 -translate-y-1/2">
                  {isCodeAvailable ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <X className="h-4 w-4 text-red-500" />
                  )}
                </div>
              )}
            </div>
            {form.formState.errors.deductionCode && (
              <p className="text-sm text-red-500">
                {form.formState.errors.deductionCode.message}
              </p>
            )}
            {!isCodeAvailable && (
              <p className="text-sm text-red-500">
                This deduction code is already taken
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="calculationMethod">
              Calculation Method <span className="text-red-500">*</span>
            </Label>
            <Select
              value={form.watch("calculationMethod")}
              onValueChange={(value) =>
                form.setValue("calculationMethod", value as DeductionCalculationMethod)
              }
            >
              <SelectTrigger
                className={cn(
                  form.formState.errors.calculationMethod && "border-red-500"
                )}
              >
                <SelectValue placeholder="Select calculation method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={DeductionCalculationMethod.FIXED}>
                  Fixed Amount
                </SelectItem>
                <SelectItem value={DeductionCalculationMethod.PERCENTAGE}>
                  Percentage
                </SelectItem>
                <SelectItem value={DeductionCalculationMethod.PROGRESSIVE}>
                  Progressive
                </SelectItem>
                <SelectItem value={DeductionCalculationMethod.FORMULA}>
                  Formula Based
                </SelectItem>
              </SelectContent>
            </Select>
            {form.formState.errors.calculationMethod && (
              <p className="text-sm text-red-500">
                {form.formState.errors.calculationMethod.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="appliesTo">Applies To</Label>
            <Select
              value={form.watch("appliesTo")}
              onValueChange={(value) =>
                form.setValue("appliesTo", value as DeductionAppliesTo)
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select what this applies to" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={DeductionAppliesTo.GROSS}>
                  Gross Salary
                </SelectItem>
                <SelectItem value={DeductionAppliesTo.BASIC}>
                  Basic Salary
                </SelectItem>
                <SelectItem value={DeductionAppliesTo.NET}>
                  Net Salary
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="amount">Amount</Label>
            <Input
              id="amount"
              type="number"
              min="0"
              step="0.01"
              placeholder="Enter deduction amount (optional)"
              {...form.register("amount", { valueAsNumber: true })}
              className={cn(
                form.formState.errors.amount && "border-red-500"
              )}
            />
            {form.formState.errors.amount && (
              <p className="text-sm text-red-500">
                {form.formState.errors.amount.message}
              </p>
            )}
            <p className="text-xs text-muted-foreground">
              Fixed amount for this deduction type (leave empty if not applicable)
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Enter description (optional)"
              rows={3}
              {...form.register("description")}
              className={cn(
                form.formState.errors.description && "border-red-500"
              )}
            />
            {form.formState.errors.description && (
              <p className="text-sm text-red-500">
                {form.formState.errors.description.message}
              </p>
            )}
          </div>

          {/* Settings */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="isSystemDefined">System Defined</Label>
                <p className="text-xs text-muted-foreground">
                  Whether this deduction type is defined by the system
                </p>
              </div>
              <Switch
                id="isSystemDefined"
                checked={form.watch("isSystemDefined")}
                onCheckedChange={(checked) =>
                  form.setValue("isSystemDefined", checked)
                }
              />
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label htmlFor="isActive">Active Status</Label>
                <p className="text-xs text-muted-foreground">
                  Whether this deduction type is currently active
                </p>
              </div>
              <Switch
                id="isActive"
                checked={form.watch("isActive")}
                onCheckedChange={(checked) => form.setValue("isActive", checked)}
              />
            </div>
          </div>
        </div>
      </form>
    </BaseSheet>
  );
}
