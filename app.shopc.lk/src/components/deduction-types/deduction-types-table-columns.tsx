"use client";

import * as React from "react";
import type { ColumnDef } from "@tanstack/react-table";
import { Checkbox } from "@/components/ui/checkbox";
import { DataTableColumnHeader } from "@/components/data-table/data-table-column-header";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Eye } from "lucide-react";
import { DeductionTypeTableData, DeductionStatus } from "@/types/deduction-type";
import { DeductionTypeStatusBadge } from "./deduction-type-status-badge";

export interface ColumnsProps {
  setRowAction: (rowAction: {
    type: "view" | "update" | "delete";
    row: any;
  }) => void;
  isDemo?: boolean;
  isActionsDisabled?: boolean;
  onRefresh?: () => void;
  onStatusUpdate?: (deductionTypeId: string, newStatus: DeductionStatus) => void;
}

export function getColumns({
  setRowAction,
  isDemo = false,
  isActionsDisabled = false,
  onRefresh,
  onStatusUpdate,
}: ColumnsProps) {
  const columns: ColumnDef<DeductionTypeTableData>[] = [
    {
      id: "select",
      header: ({ table }) => (
        <Checkbox
          checked={
            table.getIsAllPageRowsSelected() ||
            (table.getIsSomePageRowsSelected() && "indeterminate")
          }
          onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
          aria-label="Select all"
          className="translate-y-[2px]"
          disabled={isActionsDisabled}
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label="Select row"
          className="translate-y-[2px]"
          disabled={isActionsDisabled}
        />
      ),
      enableSorting: false,
      enableHiding: false,
      size: 10, // Set a fixed small width for the checkbox column
    },
    {
      accessorKey: "deductionName",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Deduction Name" />
      ),
      cell: ({ row }) => {
        const deductionName = row.getValue("deductionName") as string;
        const deductionCode = row.original.deductionCode;

        return (
          <div className="flex flex-col pl-2">
            <div className="font-medium">{deductionName}</div>
            {deductionCode && (
              <div className="text-xs text-muted-foreground">{deductionCode}</div>
            )}
          </div>
        );
      },
      enableSorting: true,
      enableHiding: false,
    },
    {
      accessorKey: "deductionCode",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Code" />
      ),
      cell: ({ row }) => {
        const code = row.getValue("deductionCode") as string;
        return (
          <div className="pl-2">
            {code ? (
              <code className="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm">
                {code}
              </code>
            ) : (
              <span className="text-xs text-muted-foreground">No code</span>
            )}
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "calculationMethod",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Calculation Method" />
      ),
      cell: ({ row }) => {
        const method = row.getValue("calculationMethod") as string;
        return (
          <div className="pl-2">
            <span className="inline-flex items-center rounded-full bg-gray-50 px-2 py-1 text-xs font-medium text-gray-700">
              {method}
            </span>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "appliesTo",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Applies To" />
      ),
      cell: ({ row }) => {
        const appliesTo = row.getValue("appliesTo") as string;
        return (
          <div className="pl-2">
            <span className="inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700">
              {appliesTo}
            </span>
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "amount",
      header: ({ column }) => (
        <DataTableColumnHeader column={column} title="Amount" />
      ),
      cell: ({ row }) => {
        const amount = row.getValue("amount") as number | undefined;
        return (
          <div className="pl-2">
            {amount !== undefined ? (
              <span className="font-mono text-sm">
                {amount.toFixed(2)}
              </span>
            ) : (
              <span className="text-xs text-muted-foreground">-</span>
            )}
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "isSystemDefined",
      header: () => <div className="font-medium">System Defined</div>,
      cell: ({ row }) => {
        const isSystemDefined = row.getValue("isSystemDefined") as boolean;
        return (
          <div className="pl-2">
            {isSystemDefined ? (
              <span className="inline-flex items-center rounded-full bg-blue-50 px-2 py-1 text-xs font-medium text-blue-700">
                Yes
              </span>
            ) : (
              <span className="inline-flex items-center rounded-full bg-gray-50 px-2 py-1 text-xs font-medium text-gray-700">
                No
              </span>
            )}
          </div>
        );
      },
      enableSorting: true,
      enableHiding: true,
    },
    {
      accessorKey: "isActive",
      header: () => <div className="font-medium">Status</div>,
      cell: ({ row }) => {
        const isActive = row.getValue("isActive") as boolean;
        return (
          <DeductionTypeStatusBadge
            deductionTypeId={row.original.id}
            isActive={isActive}
            isDemo={isDemo}
            disabled={isActionsDisabled}
            onRefresh={onRefresh}
            onStatusUpdate={onStatusUpdate}
          />
        );
      },
      enableSorting: false,
      enableHiding: false,
    },
    {
      id: "actions",
      header: () => <div className="font-medium">Actions</div>,
      cell: ({ row }) => {
        return (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                className="h-8 w-8 p-0 data-[state=open]:bg-muted"
                disabled={isActionsDisabled}
              >
                <span className="sr-only">Open menu</span>
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[160px]">
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "view",
                    row,
                  })
                }
                disabled={isActionsDisabled}
              >
                <Eye className="mr-2 h-4 w-4" />
                View Details
                <DropdownMenuShortcut>⌘V</DropdownMenuShortcut>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "update",
                    row,
                  })
                }
                disabled={isActionsDisabled}
              >
                Edit
                <DropdownMenuShortcut>⌘E</DropdownMenuShortcut>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() =>
                  setRowAction({
                    type: "delete",
                    row,
                  })
                }
                disabled={isActionsDisabled}
              >
                Delete
                <DropdownMenuShortcut>⌘⌫</DropdownMenuShortcut>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        );
      },
    },
  ];

  return columns;
}
