"use client";

import * as React from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Button } from "@/components/ui/button";
import { TrashIcon } from "@radix-ui/react-icons";
import { DeductionTypeTableData } from "@/types/deduction-type";
import { ApiStatus } from "@/types/common";
import { useBulkDeleteDeductionTypes } from "@/lib/deduction-types/hooks";
import { toast } from "sonner";

interface DeleteDeductionTypesDialogProps {
  deductionTypes: DeductionTypeTableData[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
  showTrigger?: boolean;
  onSuccess?: () => void;
  isDemo?: boolean;
}

export function DeleteDeductionTypesDialog({
  deductionTypes,
  open,
  onOpenChange,
  showTrigger = true,
  onSuccess,
  isDemo = false,
}: DeleteDeductionTypesDialogProps) {
  const bulkDeleteMutation = useBulkDeleteDeductionTypes(isDemo);

  // Check if any deduction types are system defined
  const systemDefinedDeductionTypes = deductionTypes.filter(
    (deductionType) => deductionType.isSystemDefined
  );

  const hasSystemDefinedTypes = systemDefinedDeductionTypes.length > 0;
  const isMultiple = deductionTypes.length > 1;

  const handleDelete = async () => {
    if (deductionTypes.length === 0 || hasSystemDefinedTypes) return;

    try {
      const deductionTypeIds = deductionTypes.map((deductionType) => deductionType.id);
      const result = await bulkDeleteMutation.mutateAsync({
        deductionTypeIds,
      });

      if (result.status === ApiStatus.SUCCESS && result.data) {
        toast.success(result.data.message);
        onOpenChange(false);
        onSuccess?.();
      } else {
        toast.error(result.message || "Failed to delete deduction types");
      }
    } catch (error) {
      console.error("Error deleting deduction types:", error);
      toast.error("Failed to delete deduction types");
    }
  };

  return (
    <AlertDialog open={open} onOpenChange={onOpenChange}>
      {showTrigger && (
        <AlertDialogTrigger asChild>
          <Button variant="destructive" size="sm">
            <TrashIcon className="h-4 w-4 mr-2" />
            Delete
          </Button>
        </AlertDialogTrigger>
      )}
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>
            {hasSystemDefinedTypes
              ? `Cannot Delete ${isMultiple ? "Deduction Types" : "Deduction Type"}`
              : "Are you sure?"}
          </AlertDialogTitle>
          <AlertDialogDescription>
            {hasSystemDefinedTypes ? (
              <>
                {isMultiple ? (
                  <>
                    The following deduction types cannot be deleted because they are
                    system defined:
                    <ul className="mt-2 list-disc list-inside">
                      {systemDefinedDeductionTypes.map((deductionType) => (
                        <li key={deductionType.id} className="font-medium">
                          {deductionType.deductionName} ({deductionType.deductionCode})
                        </li>
                      ))}
                    </ul>
                  </>
                ) : (
                  <>
                    The deduction type{" "}
                    <span className="font-medium">
                      {systemDefinedDeductionTypes[0]?.deductionName}
                    </span>{" "}
                    cannot be deleted because it is system defined.
                  </>
                )}
                <span className="block mt-2 text-amber-600">
                  Please change the mandatory status to optional first before deleting
                  {isMultiple ? " these deduction types" : " this deduction type"}.
                </span>
              </>
            ) : (
              <>
                This will permanently delete{" "}
                {isMultiple
                  ? `${deductionTypes.length} deduction types`
                  : "the deduction type"}{" "}
                {!isMultiple && (
                  <span className="font-medium">{deductionTypes[0]?.deductionName}</span>
                )}
                . This action cannot be undone.
                {isMultiple && (
                  <div className="mt-2">
                    <span className="text-sm font-medium">Deduction types to be deleted:</span>
                    <ul className="mt-1 list-disc list-inside text-sm">
                      {deductionTypes.slice(0, 5).map((deductionType) => (
                        <li key={deductionType.id}>
                          {deductionType.deductionName} ({deductionType.deductionCode})
                        </li>
                      ))}
                      {deductionTypes.length > 5 && (
                        <li className="text-muted-foreground">
                          ... and {deductionTypes.length - 5} more
                        </li>
                      )}
                    </ul>
                  </div>
                )}
              </>
            )}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>
            {hasSystemDefinedTypes ? "Close" : "Cancel"}
          </AlertDialogCancel>
          {!hasSystemDefinedTypes && (
            <AlertDialogAction
              onClick={handleDelete}
              disabled={bulkDeleteMutation.isPending}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {bulkDeleteMutation.isPending
                ? "Deleting..."
                : `Delete ${
                    isMultiple ? `${deductionTypes.length} Deduction Types` : "Deduction Type"
                  }`}
            </AlertDialogAction>
          )}
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
