import {
  Controller,
  Post,
  Get,
  Put,
  Delete,
  UseInterceptors,
  UploadedFile,
  UploadedFiles,
  Body,
  Param,
  Query,
  BadRequestException,
  UseGuards,
  Request,
} from '@nestjs/common';
import { FileInterceptor, FilesInterceptor } from '@nestjs/platform-express';
import {
  ApiTags,
  ApiOperation,
  ApiConsumes,
  ApiResponse,
  ApiBearerAuth,
  ApiBody,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { MediaService } from './media.service';
import { MediaDto } from './dto/media.dto';
import { MediaSignedUrlDto } from './dto/media-response.dto';
import { MediaReferenceType } from '../drizzle/schema/media.schema';
import { PermissionsGuard } from '../auth/guards/permissions.guard';
import { RequirePermissions } from '../auth/decorators/permissions.decorator';
import { Permission } from '../shared/types/permission.enum';

@ApiTags('Media')
@Controller('media')
@UseGuards(PermissionsGuard)
export class MediaController {
  constructor(private readonly mediaService: MediaService) {}

  @Post('upload')
  @ApiBearerAuth()
  @RequirePermissions(Permission.FILE_UPLOAD)
  @ApiOperation({ summary: 'Upload media file' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Media file upload',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        folder: {
          type: 'string',
          example: 'products',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Media uploaded successfully',
    type: MediaDto,
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadMedia(
    @Request() req,
    @UploadedFile() file: Express.Multer.File,
    @Body('folder') folder: string,
  ): Promise<MediaDto> {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    const businessId = req.user.businessId;
    const uploadedBy = req.user.id;

    return this.mediaService.uploadMedia(file, folder, businessId, uploadedBy);
  }

  @Post('upload-with-reference')
  @ApiBearerAuth()
  @RequirePermissions(Permission.FILE_UPLOAD)
  @ApiOperation({ summary: 'Upload media file with reference ID' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Media file upload with reference',
    schema: {
      type: 'object',
      properties: {
        file: {
          type: 'string',
          format: 'binary',
        },
        folder: {
          type: 'string',
          example: 'products',
        },
        referenceId: {
          type: 'string',
          example: '550e8400-e29b-41d4-a716-446655440000',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Media uploaded successfully with reference',
    type: MediaDto,
  })
  @UseInterceptors(FileInterceptor('file'))
  async uploadMediaWithReference(
    @Request() req,
    @UploadedFile() file: Express.Multer.File,
    @Body('folder') folder: string,
    @Body('referenceId') referenceId: string,
  ): Promise<MediaDto> {
    if (!file) {
      throw new BadRequestException('No file provided');
    }

    if (!referenceId) {
      throw new BadRequestException('Reference ID is required');
    }

    const businessId = req.user.businessId;
    const uploadedBy = req.user.id;

    return this.mediaService.uploadMediaWithReference(
      file,
      folder as MediaReferenceType,
      businessId,
      uploadedBy,
      referenceId,
    );
  }

  @Post('upload-multiple-with-reference')
  @ApiBearerAuth()
  @RequirePermissions(Permission.FILE_UPLOAD)
  @ApiOperation({ summary: 'Upload multiple media files with reference ID' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Multiple media files upload with reference',
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
        folder: {
          type: 'string',
          example: 'products',
        },
        referenceId: {
          type: 'string',
          example: '550e8400-e29b-41d4-a716-446655440000',
        },
      },
    },
  })
  @ApiResponse({
    status: 201,
    description: 'Multiple media files uploaded successfully with reference',
    type: [MediaDto],
  })
  @UseInterceptors(FilesInterceptor('files', 10))
  async uploadMultipleMediaWithReference(
    @Request() req,
    @UploadedFiles() files: Express.Multer.File[],
    @Body('folder') folder: string,
    @Body('referenceId') referenceId: string,
  ): Promise<MediaDto[]> {
    if (!files || files.length === 0) {
      throw new BadRequestException('No files provided');
    }

    if (!referenceId) {
      throw new BadRequestException('Reference ID is required');
    }

    const businessId = req.user.businessId;
    const uploadedBy = req.user.id;

    return this.mediaService.uploadMultipleMediaWithReference(
      files,
      folder,
      businessId,
      uploadedBy,
      referenceId,
    );
  }

  @Get('by-reference/:referenceId')
  @ApiBearerAuth()
  @RequirePermissions(Permission.FILE_READ)
  @ApiOperation({ summary: 'Get media files by reference ID' })
  @ApiParam({
    name: 'referenceId',
    description: 'Reference ID to find media files',
    type: 'string',
  })
  @ApiQuery({
    name: 'folder',
    description: 'Folder where the files are stored',
    required: true,
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: 'Media files retrieved successfully',
    type: [MediaSignedUrlDto],
  })
  async getMediaByReferenceId(
    @Request() req,
    @Param('referenceId') referenceId: string,
    @Query('folder') folder: string,
  ): Promise<MediaSignedUrlDto[]> {
    const businessId = req.user.businessId;
    return this.mediaService.findByReferenceIdWithSignedUrls(
      referenceId,
      businessId,
      folder,
    );
  }

  @Get(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.FILE_READ)
  @ApiOperation({ summary: 'Get media file by ID' })
  @ApiParam({
    name: 'id',
    description: 'Media ID',
    type: 'string',
  })
  @ApiQuery({
    name: 'folder',
    description: 'Folder where the file is stored',
    required: true,
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: 'Media file retrieved successfully',
    type: MediaSignedUrlDto,
  })
  async getMediaById(
    @Request() req,
    @Param('id') id: string,
    @Query('folder') folder: string,
  ): Promise<MediaSignedUrlDto> {
    const businessId = req.user.businessId;
    return this.mediaService.findByIdWithSignedUrl(id, businessId, folder);
  }

  @Put('update-reference')
  @ApiBearerAuth()
  @RequirePermissions(Permission.FILE_UPLOAD)
  @ApiOperation({ summary: 'Update media files for a reference ID' })
  @ApiConsumes('multipart/form-data')
  @ApiBody({
    description: 'Update media files for reference',
    schema: {
      type: 'object',
      properties: {
        files: {
          type: 'array',
          items: {
            type: 'string',
            format: 'binary',
          },
        },
        folder: {
          type: 'string',
          example: 'products',
        },
        referenceId: {
          type: 'string',
          example: '550e8400-e29b-41d4-a716-446655440000',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Media files updated successfully for reference',
    type: [MediaDto],
  })
  @UseInterceptors(FilesInterceptor('files', 10))
  async updateMediaForReference(
    @Request() req,
    @UploadedFiles() files: Express.Multer.File[],
    @Body('folder') folder: string,
    @Body('referenceId') referenceId: string,
  ): Promise<MediaDto[]> {
    if (!referenceId) {
      throw new BadRequestException('Reference ID is required');
    }

    const businessId = req.user.businessId;
    const uploadedBy = req.user.id;

    return this.mediaService.updateMediaForReference(
      referenceId,
      files,
      folder,
      businessId,
      uploadedBy,
    );
  }

  @Put(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.FILE_UPLOAD)
  @ApiOperation({ summary: 'Update media file metadata' })
  @ApiParam({
    name: 'id',
    description: 'Media ID',
    type: 'string',
  })
  @ApiBody({
    description: 'Media update data',
    schema: {
      type: 'object',
      properties: {
        referenceId: {
          type: 'string',
          example: '550e8400-e29b-41d4-a716-446655440000',
        },
        originalName: {
          type: 'string',
          example: 'updated-filename.jpg',
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Media file updated successfully',
    type: MediaDto,
  })
  async updateMedia(
    @Request() req,
    @Param('id') id: string,
    @Body() updateData: { referenceId?: string; originalName?: string },
  ): Promise<MediaDto> {
    const businessId = req.user.businessId;
    const updatedBy = req.user.id;

    return this.mediaService.updateMedia(id, businessId, updatedBy, updateData);
  }

  @Delete(':id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.FILE_DELETE)
  @ApiOperation({ summary: 'Delete media file' })
  @ApiParam({
    name: 'id',
    description: 'Media ID',
    type: 'string',
  })
  @ApiQuery({
    name: 'folder',
    description: 'Folder where the file is stored',
    required: false,
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: 'Media file deleted successfully',
  })
  async deleteMedia(
    @Request() req,
    @Param('id') id: string,
    @Query('folder') folder?: string,
  ): Promise<{ message: string }> {
    const businessId = req.user.businessId;
    await this.mediaService.deleteMedia(
      id,
      businessId,
      folder as MediaReferenceType,
    );
    return { message: 'Media file deleted successfully' };
  }

  @Delete('soft-delete/:id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.FILE_DELETE)
  @ApiOperation({ summary: 'Soft delete media file' })
  @ApiParam({
    name: 'id',
    description: 'Media ID',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: 'Media file soft deleted successfully',
  })
  async softDeleteMedia(
    @Request() req,
    @Param('id') id: string,
  ): Promise<{ message: string }> {
    const businessId = req.user.businessId;
    const deletedBy = req.user.id;
    await this.mediaService.softDeleteMedia(id, businessId, deletedBy);
    return { message: 'Media file soft deleted successfully' };
  }

  @Delete('by-reference/:referenceId')
  @ApiBearerAuth()
  @RequirePermissions(Permission.FILE_DELETE)
  @ApiOperation({ summary: 'Delete all media files by reference ID' })
  @ApiParam({
    name: 'referenceId',
    description: 'Reference ID to delete all associated media files',
    type: 'string',
  })
  @ApiQuery({
    name: 'folder',
    description: 'Folder where the files are stored',
    required: false,
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: 'All media files deleted successfully for reference',
  })
  async deleteMediaByReferenceId(
    @Request() req,
    @Param('referenceId') referenceId: string,
    @Query('folder') folder?: string,
  ): Promise<{ message: string }> {
    const businessId = req.user.businessId;
    await this.mediaService.deleteMediaByReferenceId(
      referenceId,
      businessId,
      folder,
    );
    return { message: 'All media files deleted successfully for reference' };
  }

  @Post('restore/:id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.FILE_UPLOAD)
  @ApiOperation({ summary: 'Restore soft deleted media file' })
  @ApiParam({
    name: 'id',
    description: 'Media ID',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: 'Media file restored successfully',
    type: MediaDto,
  })
  async restoreMedia(
    @Request() req,
    @Param('id') id: string,
  ): Promise<MediaDto> {
    const businessId = req.user.businessId;
    const restoredBy = req.user.id;
    return this.mediaService.restoreMedia(id, businessId, restoredBy);
  }

  @Get('signed-url/:id')
  @ApiBearerAuth()
  @RequirePermissions(Permission.FILE_READ)
  @ApiOperation({ summary: 'Generate signed URL for media file' })
  @ApiParam({
    name: 'id',
    description: 'Media ID',
    type: 'string',
  })
  @ApiQuery({
    name: 'folder',
    description: 'Folder where the file is stored',
    required: true,
    type: 'string',
  })
  @ApiQuery({
    name: 'expiration',
    description: 'URL expiration time in minutes (default: 60)',
    required: false,
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'Signed URL generated successfully',
    schema: {
      type: 'object',
      properties: {
        signedUrl: { type: 'string' },
      },
    },
  })
  async generateSignedUrl(
    @Request() req,
    @Param('id') id: string,
    @Query('folder') folder: string,
    @Query('expiration') expiration?: number,
  ): Promise<{ signedUrl: string }> {
    const businessId = req.user.businessId;
    const signedUrl = await this.mediaService.generateSignedUrlForMedia(
      id,
      businessId,
      folder,
      expiration || 60,
    );
    return { signedUrl };
  }

  @Post('signed-urls')
  @ApiBearerAuth()
  @RequirePermissions(Permission.FILE_READ)
  @ApiOperation({ summary: 'Generate signed URLs for multiple media files' })
  @ApiBody({
    description: 'Media IDs and folder information',
    schema: {
      type: 'object',
      properties: {
        mediaIds: {
          type: 'array',
          items: { type: 'string' },
          example: ['550e8400-e29b-41d4-a716-446655440000'],
        },
        folder: {
          type: 'string',
          example: 'products',
        },
        expiration: {
          type: 'number',
          example: 60,
        },
      },
    },
  })
  @ApiResponse({
    status: 200,
    description: 'Signed URLs generated successfully',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          signedUrl: { type: 'string' },
          originalName: { type: 'string' },
        },
      },
    },
  })
  async generateSignedUrls(
    @Request() req,
    @Body()
    body: {
      mediaIds: string[];
      folder: string;
      expiration?: number;
    },
  ): Promise<Array<{ id: string; signedUrl: string; originalName: string }>> {
    const businessId = req.user.businessId;
    const { mediaIds, folder, expiration } = body;

    if (!mediaIds || mediaIds.length === 0) {
      throw new BadRequestException('Media IDs are required');
    }

    if (!folder) {
      throw new BadRequestException('Folder is required');
    }

    return this.mediaService.generateSignedUrlsForMultipleMedia(
      mediaIds,
      businessId,
      folder,
      expiration || 60,
    );
  }
}
