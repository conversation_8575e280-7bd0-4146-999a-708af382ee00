import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsUUID,
  IsBoolean,
  IsInt,
  Min,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class AssetImageDto {
  @ApiProperty({ description: 'Asset image ID' })
  id: string;

  @ApiProperty({ description: 'Asset ID' })
  @IsNotEmpty()
  @IsUUID()
  assetId: string;

  @ApiProperty({ description: 'Media/Image ID' })
  @IsNotEmpty()
  @IsUUID()
  imageId: string;

  @ApiPropertyOptional({
    description: 'Sort order for image display',
    default: 0,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return parseInt(value, 10);
    }
    return value;
  })
  sortOrder?: number;

  @ApiPropertyOptional({
    description: 'Whether this is the primary image',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  isPrimary?: boolean;

  @ApiPropertyOptional({
    description: 'Whether the image is active',
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  isActive?: boolean;

  @ApiProperty({ description: 'Created by user ID' })
  createdBy: string;

  @ApiPropertyOptional({ description: 'Updated by user ID' })
  updatedBy?: string;

  @ApiProperty({ description: 'Creation timestamp' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update timestamp' })
  updatedAt: Date;
}

export class CreateAssetImageDto {
  @ApiProperty({ description: 'Asset ID' })
  @IsNotEmpty()
  @IsUUID()
  assetId: string;

  @ApiProperty({ description: 'Media/Image ID' })
  @IsNotEmpty()
  @IsUUID()
  imageId: string;

  @ApiPropertyOptional({
    description: 'Sort order for image display',
    default: 0,
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return parseInt(value, 10);
    }
    return value;
  })
  sortOrder?: number;

  @ApiPropertyOptional({
    description: 'Whether this is the primary image',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  isPrimary?: boolean;

  @ApiPropertyOptional({
    description: 'Whether the image is active',
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  isActive?: boolean;
}

export class UpdateAssetImageDto {
  @ApiPropertyOptional({
    description: 'Sort order for image display',
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return parseInt(value, 10);
    }
    return value;
  })
  sortOrder?: number;

  @ApiPropertyOptional({
    description: 'Whether this is the primary image',
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  isPrimary?: boolean;

  @ApiPropertyOptional({
    description: 'Whether the image is active',
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  isActive?: boolean;
}

export class UpdateAssetImagesSortOrderDto {
  @ApiProperty({
    description: 'Array of image IDs with their new sort orders',
    type: [Object],
  })
  @IsNotEmpty()
  images: Array<{
    id: string;
    sortOrder: number;
  }>;
}

export class AssetImageUpdateDto {
  @ApiProperty({ description: 'Asset image ID' })
  @IsNotEmpty()
  @IsUUID()
  imageId: string;

  @ApiPropertyOptional({
    description: 'New sort order for the image',
  })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return parseInt(value, 10);
    }
    return value;
  })
  sortOrder?: number;

  @ApiPropertyOptional({
    description: 'Whether this image should be set as primary',
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  isPrimary?: boolean;

  @ApiPropertyOptional({
    description: 'Whether the image is active',
  })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  isActive?: boolean;
}
