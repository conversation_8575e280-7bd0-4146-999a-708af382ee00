import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateAccommodationUnitDto } from './dto/create-accommodation-unit.dto';
import { UpdateAccommodationUnitDto } from './dto/update-accommodation-unit.dto';
import { AccommodationUnitDto } from './dto/accommodation-unit.dto';
import { AccommodationUnitSlimDto } from './dto/accommodation-unit-slim.dto';
import { AccommodationUnitQueryDto } from './dto/accommodation-unit-query.dto';
import { accommodationUnits } from '../drizzle/schema/accommodation-units.schema';
import { reservationTypes } from '../drizzle/schema/reservation-types.schema';
import { accounts } from '../drizzle/schema/accounts.schema';
import { taxes } from '../drizzle/schema/taxes.schema';
import { media, MediaReferenceType } from '../drizzle/schema/media.schema';
import {
  eq,
  and,
  or,
  isNull,
  ilike,
  gte,
  lte,
  inArray,
  desc,
  asc,
  sql,
  count,
} from 'drizzle-orm';
import {
  RoomStatus,
  AccommodationUnitStatus,
  TaxType,
  ActivityLogName,
} from '../shared/types';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { MediaService } from '../media/media.service';
import {
  UpdateAccommodationUnitGlobalPositionsDto,
  UpdateAccommodationUnitTypePositionsDto,
  UpdateAccommodationUnitSubTypePositionsDto,
} from './dto/update-accommodation-unit-positions.dto';

@Injectable()
export class AccommodationUnitsService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createAccommodationUnitDto: CreateAccommodationUnitDto,
    imageFiles?: Express.Multer.File[],
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check for duplicate room number within business
      if (createAccommodationUnitDto.roomNumber) {
        const existingUnit = await this.db
          .select({ id: accommodationUnits.id })
          .from(accommodationUnits)
          .where(
            and(
              eq(accommodationUnits.businessId, businessId),
              eq(
                accommodationUnits.roomNumber,
                createAccommodationUnitDto.roomNumber,
              ),
              isNull(accommodationUnits.deletedAt),
            ),
          )
          .limit(1);

        if (existingUnit.length > 0) {
          throw new ConflictException(
            `Room number '${createAccommodationUnitDto.roomNumber}' already exists`,
          );
        }
      }

      // Check for duplicate name within business
      if (createAccommodationUnitDto.name) {
        const existingUnit = await this.db
          .select({ id: accommodationUnits.id })
          .from(accommodationUnits)
          .where(
            and(
              eq(accommodationUnits.businessId, businessId),
              eq(accommodationUnits.name, createAccommodationUnitDto.name),
              isNull(accommodationUnits.deletedAt),
            ),
          )
          .limit(1);

        if (existingUnit.length > 0) {
          throw new ConflictException(
            `Unit name '${createAccommodationUnitDto.name}' already exists`,
          );
        }
      }

      // Get next positions
      const nextGlobalPosition = await this.getNextGlobalPosition(businessId);
      const nextTypePosition = await this.getNextTypePosition(
        businessId,
        createAccommodationUnitDto.type,
      );
      const nextSubTypePosition = createAccommodationUnitDto.subType
        ? await this.getNextSubTypePosition(
            businessId,
            createAccommodationUnitDto.subType,
          )
        : 0;

      // Handle image uploads
      let ogImageId: string | undefined;

      if (imageFiles && imageFiles.length > 0) {
        await this.mediaService.uploadMultipleMediaWithReference(
          imageFiles,
          'accommodation-unit',
          businessId,
          userId,
          '', // referenceId will be set after creation
        );
      }

      if (ogImageFile) {
        const ogImageResult = await this.mediaService.uploadMediaWithReference(
          ogImageFile,
          MediaReferenceType.ACCOMMODATION_UNIT_OG,
          businessId,
          userId,
          '', // referenceId will be set after creation
        );
        ogImageId = ogImageResult.id;
      }

      // Create accommodation unit
      const [newUnit] = await this.db
        .insert(accommodationUnits)
        .values({
          businessId,
          roomNumber: createAccommodationUnitDto.roomNumber,
          name: createAccommodationUnitDto.name,
          type: createAccommodationUnitDto.type,
          subType: createAccommodationUnitDto.subType,
          status: createAccommodationUnitDto.status || RoomStatus.AVAILABLE,
          floor: createAccommodationUnitDto.floor,
          building: createAccommodationUnitDto.building,
          description: createAccommodationUnitDto.description,
          bedType: createAccommodationUnitDto.bedType,
          viewType: createAccommodationUnitDto.viewType,
          maxAdults: createAccommodationUnitDto.maxAdults,
          maxChildren: createAccommodationUnitDto.maxChildren,
          basePrice: createAccommodationUnitDto.basePrice || '0.00',
          standardCost: createAccommodationUnitDto.standardCost || '0.00',
          incomeAccountId: createAccommodationUnitDto.incomeAccountId,
          expenseAccountId: createAccommodationUnitDto.expenseAccountId,
          seoTitle: createAccommodationUnitDto.seoTitle,
          seoDescription: createAccommodationUnitDto.seoDescription,
          seoKeywords: createAccommodationUnitDto.seoKeywords,
          ogImage: ogImageId,
          typePosition: nextTypePosition,
          subTypePosition: nextSubTypePosition,
          globalPosition: nextGlobalPosition,
          taxType: createAccommodationUnitDto.taxType || TaxType.INCLUSIVE,
          defaultTaxRateId: createAccommodationUnitDto.defaultTaxRateId,
          systemStatus:
            createAccommodationUnitDto.systemStatus ||
            AccommodationUnitStatus.ACTIVE,
          createdBy: userId,
        })
        .returning({ id: accommodationUnits.id });

      // Log activity
      await this.activityLogService.log(
        ActivityLogName.CREATE,
        `Created accommodation unit: ${createAccommodationUnitDto.roomNumber || createAccommodationUnitDto.name || 'Unnamed'}`,
        { id: newUnit.id, type: 'accommodation-unit' },
        { id: userId, type: 'user' },
        {
          roomNumber: createAccommodationUnitDto.roomNumber,
          name: createAccommodationUnitDto.name,
          floor: createAccommodationUnitDto.floor,
          bedType: createAccommodationUnitDto.bedType,
          maxAdults: createAccommodationUnitDto.maxAdults,
          maxChildren: createAccommodationUnitDto.maxChildren,
          businessId,
        },
      );

      return { id: newUnit.id };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create accommodation unit: ${error.message}`,
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createAccommodationUnitDto: CreateAccommodationUnitDto,
    imageFiles?: Express.Multer.File[],
    ogImageFile?: Express.Multer.File,
  ) {
    const result = await this.create(
      userId,
      businessId,
      createAccommodationUnitDto,
      imageFiles,
      ogImageFile,
    );
    return {
      id: result.id,
      message: 'Accommodation unit created successfully',
    };
  }

  private async getNextGlobalPosition(businessId: string): Promise<number> {
    const result = await this.db
      .select({
        maxPosition: sql<number>`COALESCE(MAX(${accommodationUnits.globalPosition}), -1)`,
      })
      .from(accommodationUnits)
      .where(
        and(
          eq(accommodationUnits.businessId, businessId),
          isNull(accommodationUnits.deletedAt),
        ),
      );

    return (result[0]?.maxPosition ?? -1) + 1;
  }

  private async getNextTypePosition(
    businessId: string,
    typeId: string,
  ): Promise<number> {
    const result = await this.db
      .select({
        maxPosition: sql<number>`COALESCE(MAX(${accommodationUnits.typePosition}), -1)`,
      })
      .from(accommodationUnits)
      .where(
        and(
          eq(accommodationUnits.businessId, businessId),
          eq(accommodationUnits.type, typeId),
          isNull(accommodationUnits.deletedAt),
        ),
      );

    return (result[0]?.maxPosition ?? -1) + 1;
  }

  private async getNextSubTypePosition(
    businessId: string,
    subTypeId: string,
  ): Promise<number> {
    const result = await this.db
      .select({
        maxPosition: sql<number>`COALESCE(MAX(${accommodationUnits.subTypePosition}), -1)`,
      })
      .from(accommodationUnits)
      .where(
        and(
          eq(accommodationUnits.businessId, businessId),
          eq(accommodationUnits.subType, subTypeId),
          isNull(accommodationUnits.deletedAt),
        ),
      );

    return (result[0]?.maxPosition ?? -1) + 1;
  }

  async findAll(
    businessId: string | null,
    queryDto: AccommodationUnitQueryDto,
  ) {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    const {
      page = 1,
      limit = 20,
      search,
      roomNumber,
      name,
      typeId,
      subTypeId,
      status,
      floor,
      building,
      bedType,
      viewType,
      minAdults,
      maxAdults,
      minChildren,
      maxChildren,
      minPrice,
      maxPrice,
      incomeAccountId,
      expenseAccountId,
      taxType,
      defaultTaxRateId,
      systemStatus,
      from,
      to,
      filters,
      joinOperator = 'and',
      sort,
    } = queryDto;

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      eq(accommodationUnits.businessId, businessId),
      isNull(accommodationUnits.deletedAt),
    ];

    // Add search condition
    if (search) {
      const searchCondition = or(
        ilike(accommodationUnits.roomNumber, `%${search}%`),
        ilike(accommodationUnits.name, `%${search}%`),
        ilike(accommodationUnits.description, `%${search}%`),
      );
      whereConditions.push(searchCondition);
    }

    // Add specific filters
    if (roomNumber) {
      whereConditions.push(
        ilike(accommodationUnits.roomNumber, `%${roomNumber}%`),
      );
    }
    if (name) {
      whereConditions.push(ilike(accommodationUnits.name, `%${name}%`));
    }
    if (typeId) {
      whereConditions.push(eq(accommodationUnits.type, typeId));
    }
    if (subTypeId) {
      whereConditions.push(eq(accommodationUnits.subType, subTypeId));
    }
    if (status) {
      whereConditions.push(eq(accommodationUnits.status, status));
    }
    if (floor !== undefined) {
      whereConditions.push(eq(accommodationUnits.floor, floor));
    }
    if (building) {
      whereConditions.push(ilike(accommodationUnits.building, `%${building}%`));
    }
    if (bedType) {
      whereConditions.push(eq(accommodationUnits.bedType, bedType));
    }
    if (viewType) {
      whereConditions.push(eq(accommodationUnits.viewType, viewType));
    }
    if (minAdults !== undefined) {
      whereConditions.push(gte(accommodationUnits.maxAdults, minAdults));
    }
    if (maxAdults !== undefined) {
      whereConditions.push(lte(accommodationUnits.maxAdults, maxAdults));
    }
    if (minChildren !== undefined) {
      whereConditions.push(gte(accommodationUnits.maxChildren, minChildren));
    }
    if (maxChildren !== undefined) {
      whereConditions.push(lte(accommodationUnits.maxChildren, maxChildren));
    }
    if (minPrice) {
      whereConditions.push(gte(accommodationUnits.basePrice, minPrice));
    }
    if (maxPrice) {
      whereConditions.push(lte(accommodationUnits.basePrice, maxPrice));
    }
    if (incomeAccountId) {
      whereConditions.push(
        eq(accommodationUnits.incomeAccountId, incomeAccountId),
      );
    }
    if (expenseAccountId) {
      whereConditions.push(
        eq(accommodationUnits.expenseAccountId, expenseAccountId),
      );
    }
    if (taxType) {
      whereConditions.push(eq(accommodationUnits.taxType, taxType));
    }
    if (defaultTaxRateId) {
      whereConditions.push(
        eq(accommodationUnits.defaultTaxRateId, defaultTaxRateId),
      );
    }
    if (systemStatus) {
      whereConditions.push(eq(accommodationUnits.systemStatus, systemStatus));
    }

    // Date range filters
    if (from) {
      whereConditions.push(gte(accommodationUnits.createdAt, new Date(from)));
    }
    if (to) {
      const toDate = new Date(to);
      toDate.setHours(23, 59, 59, 999);
      whereConditions.push(lte(accommodationUnits.createdAt, toDate));
    }

    // Handle advanced filters
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const advancedConditions = this.buildAdvancedFilters(parsedFilters);
        if (advancedConditions.length > 0) {
          const combinedCondition =
            joinOperator === 'or'
              ? or(...advancedConditions)
              : and(...advancedConditions);
          whereConditions.push(combinedCondition);
        }
      } catch {
        throw new BadRequestException('Invalid filters JSON format');
      }
    }

    // Build sort condition
    let orderBy: any[] = [
      accommodationUnits.globalPosition,
      accommodationUnits.roomNumber,
    ];
    if (sort) {
      const [field, direction] = sort.split(':');
      const sortDirection = direction?.toLowerCase() === 'desc' ? desc : asc;

      switch (field) {
        case 'roomNumber':
          orderBy = [sortDirection(accommodationUnits.roomNumber)];
          break;
        case 'name':
          orderBy = [sortDirection(accommodationUnits.name)];
          break;
        case 'floor':
          orderBy = [sortDirection(accommodationUnits.floor)];
          break;
        case 'basePrice':
          orderBy = [sortDirection(accommodationUnits.basePrice)];
          break;
        case 'createdAt':
          orderBy = [sortDirection(accommodationUnits.createdAt)];
          break;
        case 'globalPosition':
          orderBy = [sortDirection(accommodationUnits.globalPosition)];
          break;
        default:
          // Keep default ordering
          break;
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(accommodationUnits)
      .where(and(...whereConditions));

    const total = totalResult[0]?.count || 0;

    // Get paginated results with joins
    const results = await this.db
      .select({
        id: accommodationUnits.id,
        businessId: accommodationUnits.businessId,
        roomNumber: accommodationUnits.roomNumber,
        name: accommodationUnits.name,
        type: accommodationUnits.type,
        subType: accommodationUnits.subType,
        status: accommodationUnits.status,
        floor: accommodationUnits.floor,
        building: accommodationUnits.building,
        description: accommodationUnits.description,
        bedType: accommodationUnits.bedType,
        viewType: accommodationUnits.viewType,
        maxAdults: accommodationUnits.maxAdults,
        maxChildren: accommodationUnits.maxChildren,
        basePrice: accommodationUnits.basePrice,
        standardCost: accommodationUnits.standardCost,
        incomeAccountId: accommodationUnits.incomeAccountId,
        expenseAccountId: accommodationUnits.expenseAccountId,
        seoTitle: accommodationUnits.seoTitle,
        seoDescription: accommodationUnits.seoDescription,
        seoKeywords: accommodationUnits.seoKeywords,
        ogImage: accommodationUnits.ogImage,
        typePosition: accommodationUnits.typePosition,
        subTypePosition: accommodationUnits.subTypePosition,
        globalPosition: accommodationUnits.globalPosition,
        taxType: accommodationUnits.taxType,
        defaultTaxRateId: accommodationUnits.defaultTaxRateId,
        systemStatus: accommodationUnits.systemStatus,
        createdBy: accommodationUnits.createdBy,
        updatedBy: accommodationUnits.updatedBy,
        deletedBy: accommodationUnits.deletedBy,
        deletedAt: accommodationUnits.deletedAt,
        createdAt: accommodationUnits.createdAt,
        updatedAt: accommodationUnits.updatedAt,
        // Related entities
        typeName: reservationTypes.name,
        subTypeName: sql<string>`sub_type_rt.name`,
        incomeAccountName: sql<string>`income_account.name`,
        incomeAccountCode: sql<string>`income_account.code`,
        expenseAccountName: sql<string>`expense_account.name`,
        expenseAccountCode: sql<string>`expense_account.code`,
        taxRateName: taxes.taxName,
        taxRateRate: taxes.salesRate,
        ogImageUrl: media.signedUrl,
      })
      .from(accommodationUnits)
      .leftJoin(
        reservationTypes,
        eq(accommodationUnits.type, reservationTypes.id),
      )
      .leftJoin(
        sql`${reservationTypes} as sub_type_rt`,
        eq(accommodationUnits.subType, sql`sub_type_rt.id`),
      )
      .leftJoin(
        sql`${accounts} as income_account`,
        eq(accommodationUnits.incomeAccountId, sql`income_account.id`),
      )
      .leftJoin(
        sql`${accounts} as expense_account`,
        eq(accommodationUnits.expenseAccountId, sql`expense_account.id`),
      )
      .leftJoin(taxes, eq(accommodationUnits.defaultTaxRateId, taxes.id))
      .leftJoin(media, eq(accommodationUnits.ogImage, media.id))
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    const totalPages = Math.ceil(total / limit);

    return {
      data: results.map((result) => this.mapToAccommodationUnitDto(result)),
      total,
      page,
      limit,
      totalPages,
      hasNextPage: page < totalPages,
      hasPrevPage: page > 1,
    };
  }

  private buildAdvancedFilters(filters: any[]): any[] {
    const conditions = [];

    for (const filter of filters) {
      const { field, operator, value } = filter;

      switch (field) {
        case 'roomNumber':
          if (operator === 'contains') {
            conditions.push(ilike(accommodationUnits.roomNumber, `%${value}%`));
          } else if (operator === 'equals') {
            conditions.push(eq(accommodationUnits.roomNumber, value));
          }
          break;
        case 'name':
          if (operator === 'contains') {
            conditions.push(ilike(accommodationUnits.name, `%${value}%`));
          } else if (operator === 'equals') {
            conditions.push(eq(accommodationUnits.name, value));
          }
          break;
        case 'floor':
          if (operator === 'equals') {
            conditions.push(eq(accommodationUnits.floor, parseInt(value)));
          } else if (operator === 'gte') {
            conditions.push(gte(accommodationUnits.floor, parseInt(value)));
          } else if (operator === 'lte') {
            conditions.push(lte(accommodationUnits.floor, parseInt(value)));
          }
          break;
        case 'basePrice':
          if (operator === 'gte') {
            conditions.push(gte(accommodationUnits.basePrice, value));
          } else if (operator === 'lte') {
            conditions.push(lte(accommodationUnits.basePrice, value));
          }
          break;
        case 'maxAdults':
          if (operator === 'gte') {
            conditions.push(gte(accommodationUnits.maxAdults, parseInt(value)));
          } else if (operator === 'lte') {
            conditions.push(lte(accommodationUnits.maxAdults, parseInt(value)));
          }
          break;
        case 'maxChildren':
          if (operator === 'gte') {
            conditions.push(
              gte(accommodationUnits.maxChildren, parseInt(value)),
            );
          } else if (operator === 'lte') {
            conditions.push(
              lte(accommodationUnits.maxChildren, parseInt(value)),
            );
          }
          break;
      }
    }

    return conditions;
  }

  private mapToAccommodationUnitDto(result: any): AccommodationUnitDto {
    return {
      id: result.id,
      businessId: result.businessId,
      roomNumber: result.roomNumber,
      name: result.name,
      type: result.type,
      subType: result.subType,
      status: result.status,
      floor: result.floor,
      building: result.building,
      description: result.description,
      bedType: result.bedType,
      viewType: result.viewType,
      features: result.features,
      maxAdults: result.maxAdults,
      maxChildren: result.maxChildren,
      basePrice: result.basePrice,
      baseWeekendRate: result.baseWeekendRate,
      baseWeeklyRate: result.baseWeeklyRate,
      baseMonthlyRate: result.baseMonthlyRate,
      standardCost: result.standardCost,
      incomeAccountId: result.incomeAccountId,
      expenseAccountId: result.expenseAccountId,
      assetAccountId: result.assetAccountId,
      assetId: result.assetId,
      seoTitle: result.seoTitle,
      seoDescription: result.seoDescription,
      seoKeywords: result.seoKeywords,
      ogImage: result.ogImage,
      typePosition: result.typePosition,
      subTypePosition: result.subTypePosition,
      globalPosition: result.globalPosition,
      taxType: result.taxType,
      defaultTaxRateId: result.defaultTaxRateId,
      systemStatus: result.systemStatus,
      createdBy: result.createdBy,
      updatedBy: result.updatedBy,
      deletedBy: result.deletedBy,
      deletedAt: result.deletedAt,
      createdAt: result.createdAt,
      updatedAt: result.updatedAt,
      // Related entities
      typeInfo: result.typeName
        ? {
            id: result.type,
            name: result.typeName,
          }
        : undefined,
      subTypeInfo: result.subTypeName
        ? {
            id: result.subType,
            name: result.subTypeName,
          }
        : undefined,
      incomeAccount: result.incomeAccountName
        ? {
            id: result.incomeAccountId,
            name: result.incomeAccountName,
            code: result.incomeAccountCode,
          }
        : undefined,
      expenseAccount: result.expenseAccountName
        ? {
            id: result.expenseAccountId,
            name: result.expenseAccountName,
            code: result.expenseAccountCode,
          }
        : undefined,
      assetAccount: result.assetAccountName
        ? {
            id: result.assetAccountId,
            name: result.assetAccountName,
            code: result.assetAccountCode,
          }
        : undefined,
      asset: result.assetName
        ? {
            id: result.assetId,
            name: result.assetName,
            code: result.assetCode,
          }
        : undefined,
      defaultTaxRate: result.taxRateName
        ? {
            id: result.defaultTaxRateId,
            name: result.taxRateName,
            rate: result.taxRateRate,
          }
        : undefined,
      ogImageUrl: result.ogImageUrl,
    };
  }

  async findAllSlim(
    businessId: string | null,
  ): Promise<AccommodationUnitSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    const results = await this.db
      .select({
        id: accommodationUnits.id,
        roomNumber: accommodationUnits.roomNumber,
        name: accommodationUnits.name,
        floor: accommodationUnits.floor,
        building: accommodationUnits.building,
        bedType: accommodationUnits.bedType,
        viewType: accommodationUnits.viewType,
        maxAdults: accommodationUnits.maxAdults,
        maxChildren: accommodationUnits.maxChildren,
        basePrice: accommodationUnits.basePrice,
        status: accommodationUnits.status,
        systemStatus: accommodationUnits.systemStatus,
        typeName: reservationTypes.name,
        subTypeName: sql<string>`sub_type_rt.name`,
      })
      .from(accommodationUnits)
      .leftJoin(
        reservationTypes,
        eq(accommodationUnits.type, reservationTypes.id),
      )
      .leftJoin(
        sql`${reservationTypes} as sub_type_rt`,
        eq(accommodationUnits.subType, sql`sub_type_rt.id`),
      )
      .where(
        and(
          eq(accommodationUnits.businessId, businessId),
          isNull(accommodationUnits.deletedAt),
          eq(accommodationUnits.systemStatus, AccommodationUnitStatus.ACTIVE),
        ),
      )
      .orderBy(
        accommodationUnits.globalPosition,
        accommodationUnits.roomNumber,
      );

    return results.map((unit) => ({
      id: unit.id,
      roomNumber: unit.roomNumber,
      name: unit.name,
      floor: unit.floor,
      building: unit.building,
      bedType: unit.bedType,
      viewType: unit.viewType,
      maxAdults: unit.maxAdults,
      maxChildren: unit.maxChildren,
      basePrice: unit.basePrice,
      status: unit.status,
      systemStatus: unit.systemStatus,
      type: unit.typeName
        ? {
            id: unit.id,
            name: unit.typeName,
          }
        : undefined,
      subType: unit.subTypeName
        ? {
            id: unit.id,
            name: unit.subTypeName,
          }
        : undefined,
    }));
  }

  async findOne(id: string): Promise<AccommodationUnitDto> {
    const result = await this.db
      .select({
        id: accommodationUnits.id,
        businessId: accommodationUnits.businessId,
        roomNumber: accommodationUnits.roomNumber,
        name: accommodationUnits.name,
        type: accommodationUnits.type,
        subType: accommodationUnits.subType,
        status: accommodationUnits.status,
        floor: accommodationUnits.floor,
        building: accommodationUnits.building,
        description: accommodationUnits.description,
        bedType: accommodationUnits.bedType,
        viewType: accommodationUnits.viewType,
        maxAdults: accommodationUnits.maxAdults,
        maxChildren: accommodationUnits.maxChildren,
        basePrice: accommodationUnits.basePrice,
        standardCost: accommodationUnits.standardCost,
        incomeAccountId: accommodationUnits.incomeAccountId,
        expenseAccountId: accommodationUnits.expenseAccountId,
        seoTitle: accommodationUnits.seoTitle,
        seoDescription: accommodationUnits.seoDescription,
        seoKeywords: accommodationUnits.seoKeywords,
        ogImage: accommodationUnits.ogImage,
        typePosition: accommodationUnits.typePosition,
        subTypePosition: accommodationUnits.subTypePosition,
        globalPosition: accommodationUnits.globalPosition,
        taxType: accommodationUnits.taxType,
        defaultTaxRateId: accommodationUnits.defaultTaxRateId,
        systemStatus: accommodationUnits.systemStatus,
        createdBy: accommodationUnits.createdBy,
        updatedBy: accommodationUnits.updatedBy,
        deletedBy: accommodationUnits.deletedBy,
        deletedAt: accommodationUnits.deletedAt,
        createdAt: accommodationUnits.createdAt,
        updatedAt: accommodationUnits.updatedAt,
        // Related entities
        typeName: reservationTypes.name,
        subTypeName: sql<string>`sub_type_rt.name`,
        incomeAccountName: sql<string>`income_account.name`,
        incomeAccountCode: sql<string>`income_account.code`,
        expenseAccountName: sql<string>`expense_account.name`,
        expenseAccountCode: sql<string>`expense_account.code`,
        taxRateName: taxes.taxName,
        taxRateRate: taxes.salesRate,
        ogImageUrl: media.signedUrl,
      })
      .from(accommodationUnits)
      .leftJoin(
        reservationTypes,
        eq(accommodationUnits.type, reservationTypes.id),
      )
      .leftJoin(
        sql`${reservationTypes} as sub_type_rt`,
        eq(accommodationUnits.subType, sql`sub_type_rt.id`),
      )
      .leftJoin(
        sql`${accounts} as income_account`,
        eq(accommodationUnits.incomeAccountId, sql`income_account.id`),
      )
      .leftJoin(
        sql`${accounts} as expense_account`,
        eq(accommodationUnits.expenseAccountId, sql`expense_account.id`),
      )
      .leftJoin(taxes, eq(accommodationUnits.defaultTaxRateId, taxes.id))
      .leftJoin(media, eq(accommodationUnits.ogImage, media.id))
      .where(
        and(
          eq(accommodationUnits.id, id),
          isNull(accommodationUnits.deletedAt),
        ),
      )
      .limit(1);

    if (result.length === 0) {
      throw new NotFoundException(`Accommodation unit with ID ${id} not found`);
    }

    // Get images for this unit
    const images = await this.mediaService.findByReferenceId(
      id,
      result[0].businessId,
    );

    const unit = this.mapToAccommodationUnitDto(result[0]);
    unit.images = images.map((img) => img.fileName);

    return unit;
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateAccommodationUnitDto: UpdateAccommodationUnitDto,
    imageFiles?: Express.Multer.File[],
    ogImageFile?: Express.Multer.File,
  ) {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    // Check if unit exists and belongs to business
    const existingUnit = await this.db
      .select({
        id: accommodationUnits.id,
        businessId: accommodationUnits.businessId,
        roomNumber: accommodationUnits.roomNumber,
        name: accommodationUnits.name,
      })
      .from(accommodationUnits)
      .where(
        and(
          eq(accommodationUnits.id, id),
          eq(accommodationUnits.businessId, businessId),
          isNull(accommodationUnits.deletedAt),
        ),
      )
      .limit(1);

    if (existingUnit.length === 0) {
      throw new NotFoundException(`Accommodation unit with ID ${id} not found`);
    }

    // Check for duplicate room number (excluding current unit)
    if (
      updateAccommodationUnitDto.roomNumber &&
      updateAccommodationUnitDto.roomNumber !== existingUnit[0].roomNumber
    ) {
      const duplicateRoomNumber = await this.db
        .select({ id: accommodationUnits.id })
        .from(accommodationUnits)
        .where(
          and(
            eq(accommodationUnits.businessId, businessId),
            eq(
              accommodationUnits.roomNumber,
              updateAccommodationUnitDto.roomNumber,
            ),
            isNull(accommodationUnits.deletedAt),
            sql`${accommodationUnits.id} != ${id}`,
          ),
        )
        .limit(1);

      if (duplicateRoomNumber.length > 0) {
        throw new ConflictException(
          `Room number '${updateAccommodationUnitDto.roomNumber}' already exists`,
        );
      }
    }

    // Check for duplicate name (excluding current unit)
    if (
      updateAccommodationUnitDto.name &&
      updateAccommodationUnitDto.name !== existingUnit[0].name
    ) {
      const duplicateName = await this.db
        .select({ id: accommodationUnits.id })
        .from(accommodationUnits)
        .where(
          and(
            eq(accommodationUnits.businessId, businessId),
            eq(accommodationUnits.name, updateAccommodationUnitDto.name),
            isNull(accommodationUnits.deletedAt),
            sql`${accommodationUnits.id} != ${id}`,
          ),
        )
        .limit(1);

      if (duplicateName.length > 0) {
        throw new ConflictException(
          `Unit name '${updateAccommodationUnitDto.name}' already exists`,
        );
      }
    }

    // Handle image uploads
    if (imageFiles && imageFiles.length > 0) {
      // Delete existing images
      await this.mediaService.deleteMediaByReferenceId(
        id,
        businessId,
        'accommodation-unit',
      );

      // Upload new images
      await this.mediaService.uploadMultipleMediaWithReference(
        imageFiles,
        'accommodation-unit',
        businessId,
        userId,
        id,
      );
    }

    let ogImageId: string | undefined;
    if (ogImageFile) {
      const ogImageResult = await this.mediaService.uploadMediaWithReference(
        ogImageFile,
        MediaReferenceType.ACCOMMODATION_UNIT_OG,
        businessId,
        userId,
        id,
      );
      ogImageId = ogImageResult.id;
    }

    // Build update object
    const updateData: any = {
      updatedBy: userId,
      updatedAt: new Date(),
    };

    // Only include fields that are provided
    if (updateAccommodationUnitDto.roomNumber !== undefined) {
      updateData.roomNumber = updateAccommodationUnitDto.roomNumber;
    }
    if (updateAccommodationUnitDto.name !== undefined) {
      updateData.name = updateAccommodationUnitDto.name;
    }
    if (updateAccommodationUnitDto.type !== undefined) {
      updateData.type = updateAccommodationUnitDto.type;
    }
    if (updateAccommodationUnitDto.subType !== undefined) {
      updateData.subType = updateAccommodationUnitDto.subType;
    }
    if (updateAccommodationUnitDto.status !== undefined) {
      updateData.status = updateAccommodationUnitDto.status;
    }
    if (updateAccommodationUnitDto.floor !== undefined) {
      updateData.floor = updateAccommodationUnitDto.floor;
    }
    if (updateAccommodationUnitDto.building !== undefined) {
      updateData.building = updateAccommodationUnitDto.building;
    }
    if (updateAccommodationUnitDto.description !== undefined) {
      updateData.description = updateAccommodationUnitDto.description;
    }
    if (updateAccommodationUnitDto.bedType !== undefined) {
      updateData.bedType = updateAccommodationUnitDto.bedType;
    }
    if (updateAccommodationUnitDto.viewType !== undefined) {
      updateData.viewType = updateAccommodationUnitDto.viewType;
    }
    if (updateAccommodationUnitDto.maxAdults !== undefined) {
      updateData.maxAdults = updateAccommodationUnitDto.maxAdults;
    }
    if (updateAccommodationUnitDto.maxChildren !== undefined) {
      updateData.maxChildren = updateAccommodationUnitDto.maxChildren;
    }
    if (updateAccommodationUnitDto.basePrice !== undefined) {
      updateData.basePrice = updateAccommodationUnitDto.basePrice;
    }
    if (updateAccommodationUnitDto.standardCost !== undefined) {
      updateData.standardCost = updateAccommodationUnitDto.standardCost;
    }
    if (updateAccommodationUnitDto.incomeAccountId !== undefined) {
      updateData.incomeAccountId = updateAccommodationUnitDto.incomeAccountId;
    }
    if (updateAccommodationUnitDto.expenseAccountId !== undefined) {
      updateData.expenseAccountId = updateAccommodationUnitDto.expenseAccountId;
    }
    if (updateAccommodationUnitDto.seoTitle !== undefined) {
      updateData.seoTitle = updateAccommodationUnitDto.seoTitle;
    }
    if (updateAccommodationUnitDto.seoDescription !== undefined) {
      updateData.seoDescription = updateAccommodationUnitDto.seoDescription;
    }
    if (updateAccommodationUnitDto.seoKeywords !== undefined) {
      updateData.seoKeywords = updateAccommodationUnitDto.seoKeywords;
    }
    if (updateAccommodationUnitDto.taxType !== undefined) {
      updateData.taxType = updateAccommodationUnitDto.taxType;
    }
    if (updateAccommodationUnitDto.defaultTaxRateId !== undefined) {
      updateData.defaultTaxRateId = updateAccommodationUnitDto.defaultTaxRateId;
    }
    if (updateAccommodationUnitDto.systemStatus !== undefined) {
      updateData.systemStatus = updateAccommodationUnitDto.systemStatus;
    }
    if (ogImageId) {
      updateData.ogImage = ogImageId;
    }

    // Update the unit
    await this.db
      .update(accommodationUnits)
      .set(updateData)
      .where(eq(accommodationUnits.id, id));

    // Log activity
    await this.activityLogService.log(
      ActivityLogName.UPDATE,
      `Updated accommodation unit: ${id}`,
      { id, type: 'accommodation-unit' },
      { id: userId, type: 'user' },
      { ...updateAccommodationUnitDto, businessId },
    );

    return { id, message: 'Accommodation unit updated successfully' };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateAccommodationUnitDto: UpdateAccommodationUnitDto,
    imageFiles?: Express.Multer.File[],
    ogImageFile?: Express.Multer.File,
  ) {
    return this.update(
      userId,
      businessId,
      id,
      updateAccommodationUnitDto,
      imageFiles,
      ogImageFile,
    );
  }

  async remove(userId: string, businessId: string | null, id: string) {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    // Check if unit exists and belongs to business
    const existingUnit = await this.db
      .select({
        id: accommodationUnits.id,
        roomNumber: accommodationUnits.roomNumber,
        name: accommodationUnits.name,
      })
      .from(accommodationUnits)
      .where(
        and(
          eq(accommodationUnits.id, id),
          eq(accommodationUnits.businessId, businessId),
          isNull(accommodationUnits.deletedAt),
        ),
      )
      .limit(1);

    if (existingUnit.length === 0) {
      throw new NotFoundException(`Accommodation unit with ID ${id} not found`);
    }

    const deletedAt = new Date();

    // Soft delete the unit
    await this.db
      .update(accommodationUnits)
      .set({
        deletedBy: userId,
        deletedAt,
        updatedBy: userId,
        updatedAt: deletedAt,
      })
      .where(eq(accommodationUnits.id, id));

    // Log activity
    await this.activityLogService.log(
      ActivityLogName.DELETE,
      `Deleted accommodation unit: ${existingUnit[0].roomNumber || existingUnit[0].name || id}`,
      { id, type: 'accommodation-unit' },
      { id: userId, type: 'user' },
      {
        roomNumber: existingUnit[0].roomNumber,
        name: existingUnit[0].name,
        businessId,
      },
    );

    return {
      id,
      message: 'Accommodation unit deleted successfully',
      deletedAt,
    };
  }

  async checkRoomNumberAvailability(
    businessId: string | null,
    roomNumber: string,
    excludeId?: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    const whereConditions = [
      eq(accommodationUnits.businessId, businessId),
      eq(accommodationUnits.roomNumber, roomNumber),
      isNull(accommodationUnits.deletedAt),
    ];

    if (excludeId) {
      whereConditions.push(sql`${accommodationUnits.id} != ${excludeId}`);
    }

    const existing = await this.db
      .select({ id: accommodationUnits.id })
      .from(accommodationUnits)
      .where(and(...whereConditions))
      .limit(1);

    return { available: existing.length === 0 };
  }

  async checkNameAvailability(
    businessId: string | null,
    name: string,
    excludeId?: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    const whereConditions = [
      eq(accommodationUnits.businessId, businessId),
      eq(accommodationUnits.name, name),
      isNull(accommodationUnits.deletedAt),
    ];

    if (excludeId) {
      whereConditions.push(sql`${accommodationUnits.id} != ${excludeId}`);
    }

    const existing = await this.db
      .select({ id: accommodationUnits.id })
      .from(accommodationUnits)
      .where(and(...whereConditions))
      .limit(1);

    return { available: existing.length === 0 };
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    accommodationUnitsData: CreateAccommodationUnitDto[],
    imageFiles?: Express.Multer.File[],
  ) {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    const results = [];
    const errors = [];

    for (let i = 0; i < accommodationUnitsData.length; i++) {
      try {
        const unitData = accommodationUnitsData[i];

        // Handle images for this specific unit (if provided)
        const unitImageFiles = imageFiles
          ?.filter(
            (_, index) => Math.floor(index / 5) === i, // Assuming max 5 images per unit
          )
          .slice(0, 5);

        const result = await this.create(
          userId,
          businessId,
          unitData,
          unitImageFiles,
        );

        results.push({
          success: true,
          accommodationUnitId: result.id,
          data: unitData,
        });
      } catch (error) {
        errors.push({
          success: false,
          error: error.message,
          data: accommodationUnitsData[i],
        });
      }
    }

    const successCount = results.filter((r) => r.success).length;

    return {
      results: [...results, ...errors],
      message: `Bulk create completed. ${successCount} accommodation units created successfully, ${errors.length} failed.`,
      successCount,
      errorCount: errors.length,
    };
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    accommodationUnitsData: CreateAccommodationUnitDto[],
    imageFiles?: Express.Multer.File[],
  ) {
    const result = await this.bulkCreate(
      userId,
      businessId,
      accommodationUnitsData,
      imageFiles,
    );

    const successfulResults = result.results.filter((r) => r.success);
    const ids = successfulResults
      .map((r) => r.accommodationUnitId)
      .filter(Boolean);

    return {
      ids,
      message: result.message,
      count: successfulResults.length,
    };
  }

  async bulkDelete(userId: string, businessId: string | null, ids: string[]) {
    if (!businessId) {
      throw new UnauthorizedException('No active business found');
    }

    // Check which units exist and belong to business
    const existingUnits = await this.db
      .select({
        id: accommodationUnits.id,
        roomNumber: accommodationUnits.roomNumber,
        name: accommodationUnits.name,
      })
      .from(accommodationUnits)
      .where(
        and(
          inArray(accommodationUnits.id, ids),
          eq(accommodationUnits.businessId, businessId),
          isNull(accommodationUnits.deletedAt),
        ),
      );

    if (existingUnits.length === 0) {
      throw new NotFoundException('No accommodation units found to delete');
    }

    const deletedAt = new Date();
    const existingIds = existingUnits.map((unit) => unit.id);

    // Bulk soft delete
    await this.db
      .update(accommodationUnits)
      .set({
        deletedBy: userId,
        deletedAt,
        updatedBy: userId,
        updatedAt: deletedAt,
      })
      .where(inArray(accommodationUnits.id, existingIds));

    // Log activities
    for (const unit of existingUnits) {
      await this.activityLogService.log(
        ActivityLogName.DELETE,
        `Bulk deleted accommodation unit: ${unit.roomNumber || unit.name || unit.id}`,
        { id: unit.id, type: 'accommodation-unit' },
        { id: userId, type: 'user' },
        {
          roomNumber: unit.roomNumber,
          name: unit.name,
          bulkOperation: true,
          businessId,
        },
      );
    }

    return {
      deletedIds: existingIds,
      message: `${existingIds.length} accommodation units deleted successfully`,
      count: existingIds.length,
      deletedAt,
    };
  }

  /**
   * Update global positions for accommodation units
   * @param userId - The user ID performing the update
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  async updateAccommodationUnitGlobalPositions(
    userId: string,
    businessId: string | null,
    updates: { id: string; position: number }[],
  ): Promise<{ updated: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Additional validation for duplicate IDs and positions
      this.validatePositionUpdates(updates);

      // Validate position values (must be positive integers)
      for (const update of updates) {
        if (!Number.isInteger(update.position) || update.position < 1) {
          throw new BadRequestException(
            `Invalid position ${update.position} for accommodation unit ${update.id}. Position must be a positive integer starting from 1.`,
          );
        }
      }

      // Validate that all accommodation units exist and belong to the business
      const unitIds = updates.map((update) => update.id);
      const existingUnits = await this.db
        .select({ id: accommodationUnits.id, name: accommodationUnits.name })
        .from(accommodationUnits)
        .where(
          and(
            inArray(accommodationUnits.id, unitIds),
            eq(accommodationUnits.businessId, businessId),
            isNull(accommodationUnits.deletedAt),
          ),
        );

      if (existingUnits.length !== updates.length) {
        throw new BadRequestException(
          'Some accommodation units not found or do not belong to this business',
        );
      }

      let updatedCount = 0;

      // Use a transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        // Use optimized batch update method
        await this.batchUpdateGlobalPositions(tx, businessId, updates);
        updatedCount = updates.length;

        // Log position update activities in batch for better performance
        const activityPromises = updates.map((update) =>
          this.activityLogService.log(
            ActivityLogName.UPDATE,
            `Accommodation unit global position updated to ${update.position}`,
            { id: update.id, type: 'accommodation-unit' },
            { id: userId, type: 'user' },
            {
              accommodationUnitId: update.id,
              businessId,
              newPosition: update.position,
            },
          ),
        );

        // Execute all activity logs in parallel
        await Promise.all(activityPromises);

        // Only normalize positions if there are gaps or conflicts
        await this.conditionalNormalizeGlobalPositions(tx, businessId);
      });

      return { updated: updatedCount };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update accommodation unit global positions: ${error.message}`,
      );
    }
  }

  /**
   * Update type positions for accommodation units within their specific type
   * @param userId - The user ID performing the update
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  async updateAccommodationUnitTypePositions(
    userId: string,
    businessId: string | null,
    updates: { id: string; position: number }[],
  ): Promise<{ updated: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Additional validation for duplicate IDs and positions
      this.validatePositionUpdates(updates);

      // Validate position values (must be positive integers)
      for (const update of updates) {
        if (!Number.isInteger(update.position) || update.position < 1) {
          throw new BadRequestException(
            `Invalid position ${update.position} for accommodation unit ${update.id}. Position must be a positive integer starting from 1.`,
          );
        }
      }

      // Validate that all accommodation units exist and belong to the business
      const unitIds = updates.map((update) => update.id);
      const existingUnits = await this.db
        .select({ id: accommodationUnits.id, name: accommodationUnits.name })
        .from(accommodationUnits)
        .where(
          and(
            inArray(accommodationUnits.id, unitIds),
            eq(accommodationUnits.businessId, businessId),
            isNull(accommodationUnits.deletedAt),
          ),
        );

      if (existingUnits.length !== updates.length) {
        throw new BadRequestException(
          'Some accommodation units not found or do not belong to this business',
        );
      }

      let updatedCount = 0;

      // Use a transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        // Use optimized batch update method
        await this.batchUpdateTypePositions(tx, businessId, updates);
        updatedCount = updates.length;

        // Log position update activities in batch for better performance
        const activityPromises = updates.map((update) =>
          this.activityLogService.log(
            ActivityLogName.UPDATE,
            `Accommodation unit type position updated to ${update.position}`,
            { id: update.id, type: 'accommodation-unit' },
            { id: userId, type: 'user' },
            {
              accommodationUnitId: update.id,
              businessId,
              newPosition: update.position,
            },
          ),
        );

        // Execute all activity logs in parallel
        await Promise.all(activityPromises);

        // Only normalize positions if there are gaps or conflicts
        await this.conditionalNormalizeTypePositions(tx, businessId);
      });

      return { updated: updatedCount };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update accommodation unit type positions: ${error.message}`,
      );
    }
  }

  /**
   * Update sub-type positions for accommodation units within their specific sub-type
   * @param userId - The user ID performing the update
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  async updateAccommodationUnitSubTypePositions(
    userId: string,
    businessId: string | null,
    updates: { id: string; position: number }[],
  ): Promise<{ updated: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!updates || updates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Additional validation for duplicate IDs and positions
      this.validatePositionUpdates(updates);

      // Validate position values (must be positive integers)
      for (const update of updates) {
        if (!Number.isInteger(update.position) || update.position < 1) {
          throw new BadRequestException(
            `Invalid position ${update.position} for accommodation unit ${update.id}. Position must be a positive integer starting from 1.`,
          );
        }
      }

      // Validate that all accommodation units exist and belong to the business
      const unitIds = updates.map((update) => update.id);
      const existingUnits = await this.db
        .select({ id: accommodationUnits.id, name: accommodationUnits.name })
        .from(accommodationUnits)
        .where(
          and(
            inArray(accommodationUnits.id, unitIds),
            eq(accommodationUnits.businessId, businessId),
            isNull(accommodationUnits.deletedAt),
          ),
        );

      if (existingUnits.length !== updates.length) {
        throw new BadRequestException(
          'Some accommodation units not found or do not belong to this business',
        );
      }

      let updatedCount = 0;

      // Use a transaction to ensure all updates succeed or fail together
      await this.db.transaction(async (tx) => {
        // Use optimized batch update method
        await this.batchUpdateSubTypePositions(tx, businessId, updates);
        updatedCount = updates.length;

        // Log position update activities in batch for better performance
        const activityPromises = updates.map((update) =>
          this.activityLogService.log(
            ActivityLogName.UPDATE,
            `Accommodation unit sub-type position updated to ${update.position}`,
            { id: update.id, type: 'accommodation-unit' },
            { id: userId, type: 'user' },
            {
              accommodationUnitId: update.id,
              businessId,
              newPosition: update.position,
            },
          ),
        );

        // Execute all activity logs in parallel
        await Promise.all(activityPromises);

        // Only normalize positions if there are gaps or conflicts
        await this.conditionalNormalizeSubTypePositions(tx, businessId);
      });

      return { updated: updatedCount };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update accommodation unit sub-type positions: ${error.message}`,
      );
    }
  }

  // Legacy methods for backward compatibility
  async updateGlobalPositions(
    userId: string,
    businessId: string | null,
    positionsDto: UpdateAccommodationUnitGlobalPositionsDto,
  ) {
    const { positions } = positionsDto;
    const updates = positions.map((p) => ({ id: p.id, position: p.position }));
    const result = await this.updateAccommodationUnitGlobalPositions(
      userId,
      businessId,
      updates,
    );
    return {
      message: 'Global positions updated successfully',
      updatedCount: result.updated,
    };
  }

  async updateTypePositions(
    userId: string,
    businessId: string | null,
    positionsDto: UpdateAccommodationUnitTypePositionsDto,
  ) {
    const { positions } = positionsDto;
    const updates = positions.map((p) => ({ id: p.id, position: p.position }));
    const result = await this.updateAccommodationUnitTypePositions(
      userId,
      businessId,
      updates,
    );
    return {
      message: 'Type positions updated successfully',
      updatedCount: result.updated,
    };
  }

  async updateSubTypePositions(
    userId: string,
    businessId: string | null,
    positionsDto: UpdateAccommodationUnitSubTypePositionsDto,
  ) {
    const { positions } = positionsDto;
    const updates = positions.map((p) => ({ id: p.id, position: p.position }));
    const result = await this.updateAccommodationUnitSubTypePositions(
      userId,
      businessId,
      updates,
    );
    return {
      message: 'Sub-type positions updated successfully',
      updatedCount: result.updated,
    };
  }

  /**
   * Optimized batch global position update method
   * @param tx - Database transaction
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  private async batchUpdateGlobalPositions(
    tx: any,
    businessId: string,
    updates: { id: string; position: number }[],
  ): Promise<void> {
    try {
      if (updates.length === 0) return;

      // Sort updates by target position to minimize conflicts
      const sortedUpdates = [...updates].sort(
        (a, b) => a.position - b.position,
      );

      // For small batches, use individual updates which are more reliable
      if (sortedUpdates.length <= 5) {
        for (const update of sortedUpdates) {
          await tx
            .update(accommodationUnits)
            .set({
              globalPosition: update.position,
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(accommodationUnits.id, update.id),
                eq(accommodationUnits.businessId, businessId),
                isNull(accommodationUnits.deletedAt),
              ),
            );
        }
        return;
      }

      // For larger batches, use parallel individual updates
      const updatePromises = sortedUpdates.map((update) =>
        tx
          .update(accommodationUnits)
          .set({
            globalPosition: update.position,
            updatedAt: new Date(),
          })
          .where(
            and(
              eq(accommodationUnits.id, update.id),
              eq(accommodationUnits.businessId, businessId),
              isNull(accommodationUnits.deletedAt),
            ),
          ),
      );

      // Execute all updates in parallel for better performance
      await Promise.all(updatePromises);
    } catch (error) {
      console.warn('Failed to batch update global positions:', error.message);
      throw error;
    }
  }

  /**
   * Optimized batch type position update method
   * @param tx - Database transaction
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  private async batchUpdateTypePositions(
    tx: any,
    businessId: string,
    updates: { id: string; position: number }[],
  ): Promise<void> {
    try {
      if (updates.length === 0) return;

      // Sort updates by target position to minimize conflicts
      const sortedUpdates = [...updates].sort(
        (a, b) => a.position - b.position,
      );

      // For small batches, use individual updates which are more reliable
      if (sortedUpdates.length <= 5) {
        for (const update of sortedUpdates) {
          await tx
            .update(accommodationUnits)
            .set({
              typePosition: update.position,
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(accommodationUnits.id, update.id),
                eq(accommodationUnits.businessId, businessId),
                isNull(accommodationUnits.deletedAt),
              ),
            );
        }
        return;
      }

      // For larger batches, use parallel individual updates
      const updatePromises = sortedUpdates.map((update) =>
        tx
          .update(accommodationUnits)
          .set({
            typePosition: update.position,
            updatedAt: new Date(),
          })
          .where(
            and(
              eq(accommodationUnits.id, update.id),
              eq(accommodationUnits.businessId, businessId),
              isNull(accommodationUnits.deletedAt),
            ),
          ),
      );

      // Execute all updates in parallel for better performance
      await Promise.all(updatePromises);
    } catch (error) {
      console.warn('Failed to batch update type positions:', error.message);
      throw error;
    }
  }

  /**
   * Optimized batch sub-type position update method
   * @param tx - Database transaction
   * @param businessId - The business ID
   * @param updates - Array of position updates
   */
  private async batchUpdateSubTypePositions(
    tx: any,
    businessId: string,
    updates: { id: string; position: number }[],
  ): Promise<void> {
    try {
      if (updates.length === 0) return;

      // Sort updates by target position to minimize conflicts
      const sortedUpdates = [...updates].sort(
        (a, b) => a.position - b.position,
      );

      // For small batches, use individual updates which are more reliable
      if (sortedUpdates.length <= 5) {
        for (const update of sortedUpdates) {
          await tx
            .update(accommodationUnits)
            .set({
              subTypePosition: update.position,
              updatedAt: new Date(),
            })
            .where(
              and(
                eq(accommodationUnits.id, update.id),
                eq(accommodationUnits.businessId, businessId),
                isNull(accommodationUnits.deletedAt),
              ),
            );
        }
        return;
      }

      // For larger batches, use parallel individual updates
      const updatePromises = sortedUpdates.map((update) =>
        tx
          .update(accommodationUnits)
          .set({
            subTypePosition: update.position,
            updatedAt: new Date(),
          })
          .where(
            and(
              eq(accommodationUnits.id, update.id),
              eq(accommodationUnits.businessId, businessId),
              isNull(accommodationUnits.deletedAt),
            ),
          ),
      );

      // Execute all updates in parallel for better performance
      await Promise.all(updatePromises);
    } catch (error) {
      console.warn('Failed to batch update sub-type positions:', error.message);
      throw error;
    }
  }

  /**
   * Validate position updates for duplicates and other issues
   * @param updates - Array of position updates
   */
  private validatePositionUpdates(
    updates: { id: string; position: number }[],
  ): void {
    // Check for duplicate IDs
    const ids = updates.map((u) => u.id);
    const duplicateIds = ids.filter((id, index) => ids.indexOf(id) !== index);
    if (duplicateIds.length > 0) {
      throw new BadRequestException(
        `Duplicate accommodation unit IDs found: ${duplicateIds.join(', ')}`,
      );
    }

    // Check for duplicate positions
    const positions = updates.map((u) => u.position);
    const duplicatePositions = positions.filter(
      (pos, index) => positions.indexOf(pos) !== index,
    );
    if (duplicatePositions.length > 0) {
      throw new BadRequestException(
        `Duplicate positions found: ${duplicatePositions.join(', ')}`,
      );
    }
  }

  /**
   * Conditionally normalize global positions if there are gaps or conflicts
   * @param _tx - Database transaction
   * @param _businessId - The business ID
   */
  private async conditionalNormalizeGlobalPositions(
    _tx: any,
    _businessId: string,
  ): Promise<void> {
    // This is a placeholder for position normalization logic
    // Implementation can be added later if needed
  }

  /**
   * Conditionally normalize type positions if there are gaps or conflicts
   * @param _tx - Database transaction
   * @param _businessId - The business ID
   */
  private async conditionalNormalizeTypePositions(
    _tx: any,
    _businessId: string,
  ): Promise<void> {
    // This is a placeholder for position normalization logic
    // Implementation can be added later if needed
  }

  /**
   * Conditionally normalize sub-type positions if there are gaps or conflicts
   * @param _tx - Database transaction
   * @param _businessId - The business ID
   */
  private async conditionalNormalizeSubTypePositions(
    _tx: any,
    _businessId: string,
  ): Promise<void> {
    // This is a placeholder for position normalization logic
    // Implementation can be added later if needed
  }
}
