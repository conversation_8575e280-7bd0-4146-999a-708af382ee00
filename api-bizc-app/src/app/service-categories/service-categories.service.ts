import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateServiceCategoryDto } from './dto/create-service-category.dto';
import { UpdateServiceCategoryDto } from './dto/update-service-category.dto';
import { ServiceCategoryDto } from './dto/service-category.dto';
import { ServiceCategorySlimDto } from './dto/service-category-slim.dto';
import { ServiceCategoryListDto } from './dto/service-category-list.dto';
import {
  serviceCategories,
  serviceCategoryLocations,
} from '../drizzle/schema/service-categories.schema';
import { services } from '../drizzle/schema/services.schema';
import { locations } from '../drizzle/schema/locations.schema';
import { users } from '../drizzle/schema/users.schema';
import { media, MediaReferenceType } from '../drizzle/schema/media.schema';
import {
  and,
  eq,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  desc,
  asc,
  or,
  inArray,
} from 'drizzle-orm';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { MediaService } from '../media/media.service';
import { GcsUploadService } from '../gcs-upload/gcs-upload.service';
import { UsersService } from '../users/users.service';
import { LocationsService } from '../locations/locations.service';
import { ServiceCategoryStatus, ActivityLogName } from '../shared/types';

@Injectable()
export class ServiceCategoriesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
    private readonly gcsUploadService: GcsUploadService,
    private readonly usersService: UsersService,
    private readonly locationsService: LocationsService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createServiceCategoryDto: CreateServiceCategoryDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a service category with the same name already exists for this business
      // Using ilike for case-insensitive comparison
      const existingServiceCategory = await this.db
        .select()
        .from(serviceCategories)
        .where(
          and(
            eq(serviceCategories.businessId, businessId),
            ilike(serviceCategories.name, createServiceCategoryDto.name),
            isNull(serviceCategories.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (existingServiceCategory) {
        throw new ConflictException(
          `Service category with name "${createServiceCategoryDto.name}" already exists`,
        );
      }

      // Check if shortCode is provided and unique
      if (createServiceCategoryDto.shortCode) {
        const existingShortCode = await this.db
          .select()
          .from(serviceCategories)
          .where(
            and(
              eq(serviceCategories.businessId, businessId),
              ilike(
                serviceCategories.shortCode,
                createServiceCategoryDto.shortCode,
              ),
              isNull(serviceCategories.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (existingShortCode) {
          throw new ConflictException(
            `Service category with short code "${createServiceCategoryDto.shortCode}" already exists`,
          );
        }
      }

      // Check if slug is provided and unique
      if (createServiceCategoryDto.slug) {
        const existingSlug = await this.db
          .select()
          .from(serviceCategories)
          .where(
            and(
              eq(serviceCategories.businessId, businessId),
              ilike(serviceCategories.slug, createServiceCategoryDto.slug),
              isNull(serviceCategories.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (existingSlug) {
          throw new ConflictException(
            `Service category with slug "${createServiceCategoryDto.slug}" already exists`,
          );
        }
      }

      // Validate parent service category if provided
      if (createServiceCategoryDto.parentId) {
        const parentServiceCategory = await this.db
          .select()
          .from(serviceCategories)
          .where(
            and(
              eq(serviceCategories.id, createServiceCategoryDto.parentId),
              eq(serviceCategories.businessId, businessId),
              isNull(serviceCategories.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (!parentServiceCategory) {
          throw new BadRequestException('Parent service category not found');
        }
      }

      // Get the next position if not provided
      let position = createServiceCategoryDto.position ?? 0;
      if (position === 0) {
        const maxPositionResult = await this.db
          .select({
            maxPosition: sql<number>`max(${serviceCategories.position})`,
          })
          .from(serviceCategories)
          .where(
            and(
              eq(serviceCategories.businessId, businessId),
              isNull(serviceCategories.deletedAt),
            ),
          );
        position = (maxPositionResult[0]?.maxPosition || 0) + 1;
      }

      // Create the service category
      const [newServiceCategory] = await this.db
        .insert(serviceCategories)
        .values({
          businessId,
          name: createServiceCategoryDto.name,
          shortCode: createServiceCategoryDto.shortCode,
          parentId: createServiceCategoryDto.parentId,
          description: createServiceCategoryDto.description,
          slug: createServiceCategoryDto.slug,
          availableOnline: createServiceCategoryDto.availableOnline ?? false,
          position,
          color: createServiceCategoryDto.color,
          status:
            createServiceCategoryDto.status ?? ServiceCategoryStatus.ACTIVE,
          seoTitle: createServiceCategoryDto.seoTitle,
          seoDescription: createServiceCategoryDto.seoDescription,
          seoKeywords: createServiceCategoryDto.seoKeywords,
          isAllocatedToAllLocations:
            createServiceCategoryDto.isAllocatedToAllLocations ?? true,
          createdBy: userId,
          updatedBy: userId,
        })
        .returning();

      // Handle image upload
      if (imageFile) {
        try {
          const imageMedia = await this.mediaService.uploadMediaWithReference(
            imageFile,
            MediaReferenceType.SERVICE_CATEGORIES_IMAGES,
            businessId,
            userId,
            newServiceCategory.id,
          );

          // Update service category with image reference
          await this.db
            .update(serviceCategories)
            .set({ image: imageMedia.id })
            .where(eq(serviceCategories.id, newServiceCategory.id));
        } catch (error) {
          console.warn(
            'Failed to upload service category image:',
            error.message,
          );
        }
      }

      // Handle OG image upload
      if (ogImageFile) {
        try {
          const ogImageMedia = await this.mediaService.uploadMediaWithReference(
            ogImageFile,
            MediaReferenceType.SERVICE_CATEGORIES_OG_IMAGES,
            businessId,
            userId,
            newServiceCategory.id,
          );

          // Update service category with OG image reference
          await this.db
            .update(serviceCategories)
            .set({ ogImage: ogImageMedia.id })
            .where(eq(serviceCategories.id, newServiceCategory.id));
        } catch (error) {
          console.warn('Failed to upload OG image:', error.message);
        }
      }

      // Handle location assignments
      if (
        !createServiceCategoryDto.isAllocatedToAllLocations &&
        createServiceCategoryDto.locationIds
      ) {
        // Validate location IDs
        const validLocations = await this.db
          .select({ id: locations.id })
          .from(locations)
          .where(
            and(
              eq(locations.businessId, businessId),
              inArray(locations.id, createServiceCategoryDto.locationIds),
              isNull(locations.deletedAt),
            ),
          );

        if (
          validLocations.length !== createServiceCategoryDto.locationIds.length
        ) {
          throw new BadRequestException('One or more location IDs are invalid');
        }

        // Insert location associations
        const locationAssociations = createServiceCategoryDto.locationIds.map(
          (locationId) => ({
            serviceCategoryId: newServiceCategory.id,
            locationId,
            createdBy: userId,
            updatedBy: userId,
          }),
        );

        await this.db
          .insert(serviceCategoryLocations)
          .values(locationAssociations);
      }

      // Log activity
      await this.activityLogService.log(
        ActivityLogName.CREATE,
        `Created service category: ${createServiceCategoryDto.name}`,
        { id: newServiceCategory.id, type: 'service-category' },
        { id: userId, type: 'user' },
        { serviceCategoryId: newServiceCategory.id, businessId },
      );

      return { id: newServiceCategory.id };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create service category: ${error.message}`,
      );
    }
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createServiceCategoryDto: CreateServiceCategoryDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    return this.create(
      userId,
      businessId,
      createServiceCategoryDto,
      imageFile,
      ogImageFile,
    );
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createServiceCategoryDtos: CreateServiceCategoryDto[],
    imageFiles?: Express.Multer.File[],
  ): Promise<string[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const createdIds: string[] = [];
    const errors: string[] = [];

    for (let i = 0; i < createServiceCategoryDtos.length; i++) {
      try {
        const createServiceCategoryDto = createServiceCategoryDtos[i];

        // Handle image assignment based on imageIndex
        let serviceCategoryImage: Express.Multer.File | undefined;
        if (
          imageFiles &&
          'imageIndex' in createServiceCategoryDto &&
          typeof createServiceCategoryDto.imageIndex === 'number'
        ) {
          const imageIndex = createServiceCategoryDto.imageIndex;
          if (imageIndex >= 0 && imageIndex < imageFiles.length) {
            serviceCategoryImage = imageFiles[imageIndex];
          }
        }

        const result = await this.create(
          userId,
          businessId,
          createServiceCategoryDto,
          serviceCategoryImage,
        );
        createdIds.push(result.id);
      } catch (error) {
        errors.push(`Service category ${i + 1}: ${error.message}`);
      }
    }

    if (errors.length > 0 && createdIds.length === 0) {
      throw new BadRequestException(
        `Failed to create service categories: ${errors.join(', ')}`,
      );
    }

    return createdIds;
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createServiceCategoryDtos: CreateServiceCategoryDto[],
    imageFiles?: Express.Multer.File[],
  ): Promise<{ ids: string[] }> {
    const ids = await this.bulkCreate(
      userId,
      businessId,
      createServiceCategoryDtos,
      imageFiles,
    );
    return { ids };
  }

  async findOne(
    id: string,
    businessId: string | null,
  ): Promise<ServiceCategoryDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get the service category with related data
    const serviceCategory = await this.db
      .select({
        id: serviceCategories.id,
        businessId: serviceCategories.businessId,
        name: serviceCategories.name,
        shortCode: serviceCategories.shortCode,
        parentId: serviceCategories.parentId,
        description: serviceCategories.description,
        slug: serviceCategories.slug,
        availableOnline: serviceCategories.availableOnline,
        position: serviceCategories.position,
        color: serviceCategories.color,
        image: serviceCategories.image,
        seoTitle: serviceCategories.seoTitle,
        seoDescription: serviceCategories.seoDescription,
        seoKeywords: serviceCategories.seoKeywords,
        ogImage: serviceCategories.ogImage,
        isAllocatedToAllLocations: serviceCategories.isAllocatedToAllLocations,
        status: serviceCategories.status,
        createdBy: serviceCategories.createdBy,
        updatedBy: serviceCategories.updatedBy,
        createdAt: serviceCategories.createdAt,
        updatedAt: serviceCategories.updatedAt,
        createdByName: sql<string>`created_user.name`.as('createdByName'),
        updatedByName: sql<string>`updated_user.name`.as('updatedByName'),
      })
      .from(serviceCategories)
      .leftJoin(
        sql`${users} created_user`,
        sql`${serviceCategories.createdBy} = created_user.id`,
      )
      .leftJoin(
        sql`${users} updated_user`,
        sql`${serviceCategories.updatedBy} = updated_user.id`,
      )
      .where(
        and(
          eq(serviceCategories.id, id),
          eq(serviceCategories.businessId, businessId),
          isNull(serviceCategories.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!serviceCategory) {
      throw new NotFoundException('Service category not found');
    }

    return this.mapToServiceCategoryDto(serviceCategory);
  }

  private async mapToServiceCategoryDto(
    serviceCategory: any,
  ): Promise<ServiceCategoryDto> {
    // Get location associations
    const locationAssociations = await this.db
      .select({
        id: locations.id,
        name: locations.name,
      })
      .from(serviceCategoryLocations)
      .innerJoin(
        locations,
        eq(serviceCategoryLocations.locationId, locations.id),
      )
      .where(
        eq(serviceCategoryLocations.serviceCategoryId, serviceCategory.id),
      );

    // Get services count
    const servicesCount = await this.getServicesCountForServiceCategory(
      serviceCategory.id,
      serviceCategory.businessId,
    );

    // Create the DTO
    const serviceCategoryDto: ServiceCategoryDto = {
      id: serviceCategory.id,
      businessId: serviceCategory.businessId,
      name: serviceCategory.name,
      shortCode: serviceCategory.shortCode,
      parentId: serviceCategory.parentId,
      description: serviceCategory.description,
      slug: serviceCategory.slug,
      availableOnline: serviceCategory.availableOnline,
      position: serviceCategory.position,
      color: serviceCategory.color,
      seoTitle: serviceCategory.seoTitle,
      seoDescription: serviceCategory.seoDescription,
      seoKeywords: serviceCategory.seoKeywords,
      isAllocatedToAllLocations: serviceCategory.isAllocatedToAllLocations,
      locations: locationAssociations,
      createdBy: serviceCategory.createdByName || 'Unknown',
      updatedBy: serviceCategory.updatedByName,
      status: serviceCategory.status,
      servicesCount,
      createdAt: serviceCategory.createdAt,
      updatedAt: serviceCategory.updatedAt,
    };

    // Fetch media information and generate signed URL if image exists
    if (serviceCategory.image) {
      try {
        const mediaData = await this.mediaService.findById(
          serviceCategory.image,
          serviceCategory.businessId,
        );

        // Generate signed URL with 60 minutes expiration for the image
        serviceCategoryDto.image =
          await this.gcsUploadService.generateSignedUrl(
            mediaData.fileName,
            'service-categories', // folder where service category images are stored
            60, // expiration in minutes
          );
      } catch (error) {
        // If media is not found or signed URL generation fails, just continue without it
        console.warn(
          `Failed to generate signed URL for service category ${serviceCategory.id} image:`,
          error.message,
        );
      }
    }

    // Fetch OG image information and generate signed URL if exists
    if (serviceCategory.ogImage) {
      try {
        const ogImageData = await this.mediaService.findById(
          serviceCategory.ogImage,
          serviceCategory.businessId,
        );

        // Generate signed URL with 60 minutes expiration for the OG image
        serviceCategoryDto.ogImage =
          await this.gcsUploadService.generateSignedUrl(
            ogImageData.fileName,
            'service-categories', // folder where service category OG images are stored
            60, // expiration in minutes
          );
      } catch (error) {
        // If media is not found or signed URL generation fails, just continue without it
        console.warn(
          `Failed to generate signed URL for service category ${serviceCategory.id} OG image:`,
          error.message,
        );
      }
    }

    return serviceCategoryDto;
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    slug?: string,
    shortCode?: string,
    status?: string,
    availableOnline?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: ServiceCategoryListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      isNull(serviceCategories.deletedAt),
      eq(serviceCategories.businessId, businessId),
    ];

    // Add date range filters
    if (from) {
      whereConditions.push(gte(serviceCategories.createdAt, new Date(from)));
    }
    if (to) {
      whereConditions.push(lte(serviceCategories.createdAt, new Date(to)));
    }

    // Add search filters
    if (name) {
      whereConditions.push(ilike(serviceCategories.name, `%${name}%`));
    }
    if (slug) {
      whereConditions.push(ilike(serviceCategories.slug, `%${slug}%`));
    }
    if (shortCode) {
      whereConditions.push(
        ilike(serviceCategories.shortCode, `%${shortCode}%`),
      );
    }
    // Add status filtering if provided
    if (status) {
      // Decode URL-encoded commas and split by comma
      const decodedStatus = decodeURIComponent(status);
      const statusArray = decodedStatus
        .split(',')
        .map((s) => s.trim() as ServiceCategoryStatus);
      whereConditions.push(inArray(serviceCategories.status, statusArray));
    }
    // Add availableOnline filtering if provided
    if (availableOnline) {
      // Decode URL-encoded commas and split by comma
      const decodedAvailableOnline = decodeURIComponent(availableOnline);
      const availableOnlineValues = decodedAvailableOnline
        .split(',')
        .map((s) => s.trim());
      if (availableOnlineValues.length === 1) {
        const boolValue = availableOnlineValues[0].toLowerCase() === 'true';
        whereConditions.push(eq(serviceCategories.availableOnline, boolValue));
      } else {
        // Handle multiple boolean values (true, false)
        const boolValues = availableOnlineValues.map(
          (s) => s.toLowerCase() === 'true',
        );
        if (boolValues.includes(true) && boolValues.includes(false)) {
          // If both true and false are included, no filtering needed (all records)
        } else if (boolValues.includes(true)) {
          whereConditions.push(eq(serviceCategories.availableOnline, true));
        } else if (boolValues.includes(false)) {
          whereConditions.push(eq(serviceCategories.availableOnline, false));
        }
      }
    }

    // Add advanced filters if provided
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          const { id: fieldId, value, operator } = filter;

          if (fieldId === 'name') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(serviceCategories.name, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(serviceCategories.name, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(serviceCategories.name, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${serviceCategories.name} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${serviceCategories.name} IS NULL OR ${serviceCategories.name} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${serviceCategories.name} IS NOT NULL AND ${serviceCategories.name} != ''`,
                );
                break;
            }
          } else if (fieldId === 'status') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(
                      serviceCategories.status,
                      value as ServiceCategoryStatus[],
                    ),
                  );
                } else {
                  filterConditions.push(eq(serviceCategories.status, value));
                }
                break;
              case 'ne':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    sql`${serviceCategories.status} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                  );
                } else {
                  filterConditions.push(
                    sql`${serviceCategories.status} != ${value}`,
                  );
                }
                break;
              case 'iLike': // Contains (for backward compatibility)
                if (typeof value === 'string') {
                  const decodedValue = decodeURIComponent(value);
                  const statusValues = decodedValue
                    .split(',')
                    .map((s) => s.trim() as ServiceCategoryStatus);
                  filterConditions.push(
                    inArray(serviceCategories.status, statusValues),
                  );
                } else {
                  filterConditions.push(eq(serviceCategories.status, value));
                }
                break;
            }
          } else if (fieldId === 'slug') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(serviceCategories.slug, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(serviceCategories.slug, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(serviceCategories.slug, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${serviceCategories.slug} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${serviceCategories.slug} IS NULL OR ${serviceCategories.slug} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${serviceCategories.slug} IS NOT NULL AND ${serviceCategories.slug} != ''`,
                );
                break;
            }
          } else if (fieldId === 'shortCode') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(serviceCategories.shortCode, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(serviceCategories.shortCode, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(serviceCategories.shortCode, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${serviceCategories.shortCode} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${serviceCategories.shortCode} IS NULL OR ${serviceCategories.shortCode} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${serviceCategories.shortCode} IS NOT NULL AND ${serviceCategories.shortCode} != ''`,
                );
                break;
            }
          } else if (fieldId === 'availableOnline') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  const boolValues = value.map(
                    (v) => v === 'true' || v === true,
                  );
                  if (boolValues.includes(true) && boolValues.includes(false)) {
                    // Both true and false, no filtering needed
                  } else if (boolValues.includes(true)) {
                    filterConditions.push(
                      eq(serviceCategories.availableOnline, true),
                    );
                  } else if (boolValues.includes(false)) {
                    filterConditions.push(
                      eq(serviceCategories.availableOnline, false),
                    );
                  }
                } else {
                  const boolValue = value === 'true' || value === true;
                  filterConditions.push(
                    eq(serviceCategories.availableOnline, boolValue),
                  );
                }
                break;
              case 'ne': {
                const boolValue = value === 'true' || value === true;
                filterConditions.push(
                  eq(serviceCategories.availableOnline, !boolValue),
                );
                break;
              }
              case 'iLike': // Contains (for backward compatibility)
                if (typeof value === 'string') {
                  const decodedValue = decodeURIComponent(value);
                  const availableOnlineValues = decodedValue
                    .split(',')
                    .map((s) => s.trim());
                  const boolValues = availableOnlineValues.map(
                    (s) => s.toLowerCase() === 'true',
                  );
                  if (boolValues.includes(true) && boolValues.includes(false)) {
                    // Both true and false, no filtering needed
                  } else if (boolValues.includes(true)) {
                    filterConditions.push(
                      eq(serviceCategories.availableOnline, true),
                    );
                  } else if (boolValues.includes(false)) {
                    filterConditions.push(
                      eq(serviceCategories.availableOnline, false),
                    );
                  }
                } else {
                  const boolValue = value === 'true' || value === true;
                  filterConditions.push(
                    eq(serviceCategories.availableOnline, boolValue),
                  );
                }
                break;
            }
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Build optimized sort conditions with proper indexing strategy
    // Default sort: position ascending, then by ID for consistent pagination when positions are equal
    // This leverages the composite index for optimal performance
    let orderBy = [asc(serviceCategories.position), asc(serviceCategories.id)];

    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          const isDesc = sortField.desc === true;

          switch (sortField.id) {
            case 'name':
              // Use name index for sorting
              orderBy = [
                isDesc
                  ? desc(serviceCategories.name)
                  : asc(serviceCategories.name),
                asc(serviceCategories.id), // Secondary sort for consistency
              ];
              break;
            case 'position':
              // Optimized position sorting with secondary sort for pagination consistency
              orderBy = [
                isDesc
                  ? desc(serviceCategories.position)
                  : asc(serviceCategories.position),
                asc(serviceCategories.id), // Use ID for consistent pagination when positions are equal
              ];
              break;
            case 'createdAt':
              orderBy = [
                isDesc
                  ? desc(serviceCategories.createdAt)
                  : asc(serviceCategories.createdAt),
                asc(serviceCategories.id), // Secondary sort for consistency
              ];
              break;
            case 'updatedAt':
              orderBy = [
                isDesc
                  ? desc(serviceCategories.updatedAt)
                  : asc(serviceCategories.updatedAt),
                asc(serviceCategories.id), // Secondary sort for consistency
              ];
              break;
            case 'servicesCount':
              // These will be handled with post-query sorting since they are calculated fields
              // Use position as default to maintain performance
              orderBy = [
                asc(serviceCategories.position),
                asc(serviceCategories.id),
              ];
              break;
          }
        }
      } catch {
        // Invalid JSON, use default sort
      }
    }

    // Get service categories with user information and image data
    const result = await this.db
      .select({
        id: serviceCategories.id,
        name: serviceCategories.name,
        shortCode: serviceCategories.shortCode,
        slug: serviceCategories.slug,
        description: serviceCategories.description,
        status: serviceCategories.status,
        availableOnline: serviceCategories.availableOnline,
        position: serviceCategories.position,
        color: serviceCategories.color,
        parentId: serviceCategories.parentId,
        imageId: serviceCategories.image,
        imagePublicUrl: media.publicUrl,
        imageFileName: media.fileName,
        createdAt: serviceCategories.createdAt,
        updatedAt: serviceCategories.updatedAt,
      })
      .from(serviceCategories)
      .leftJoin(media, eq(serviceCategories.image, media.id))
      .leftJoin(
        sql`${users} created_user`,
        sql`${serviceCategories.createdBy} = created_user.id`,
      )
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    // Get total count for pagination
    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(serviceCategories)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    // Get service category IDs for additional data
    const serviceCategoryIds = result.map((st) => st.id);

    // Get parent information
    const parentInfoQuery = await this.db
      .select({
        id: serviceCategories.id,
        parentId: serviceCategories.parentId,
        parentName: sql<string>`parent_st.name`.as('parentName'),
      })
      .from(serviceCategories)
      .leftJoin(
        sql`${serviceCategories} parent_st`,
        sql`${serviceCategories.parentId} = parent_st.id`,
      )
      .where(
        and(
          inArray(serviceCategories.id, serviceCategoryIds),
          isNull(serviceCategories.deletedAt),
        ),
      );

    // Get sub service categories count for each service category
    const subServiceCategoriesCountQuery = await this.db
      .select({
        parentId: serviceCategories.parentId,
        count: sql<number>`count(*)`.as('count'),
      })
      .from(serviceCategories)
      .where(
        and(
          inArray(serviceCategories.parentId, serviceCategoryIds),
          isNull(serviceCategories.deletedAt),
        ),
      )
      .groupBy(serviceCategories.parentId);

    // Get services count for each service category
    const servicesCountMap = await this.getServicesCountForServiceCategories(
      serviceCategoryIds,
      businessId,
    );

    // Create lookup maps
    const parentInfoMap = new Map(
      parentInfoQuery.map((p) => [p.id, p.parentName]),
    );
    const subServiceCategoriesCountMap = new Map(
      subServiceCategoriesCountQuery.map((s) => [s.parentId, s.count]),
    );

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.VIEW,
      'Viewed service categories (optimized)',
      { id: businessId, type: 'business' },
      { id: userId, type: 'user' },
    );

    // Generate signed URLs for images and build final data
    const data = await Promise.all(
      result.map(async (serviceCategory) => {
        let imageUrl: string | undefined;

        if (serviceCategory.imageFileName) {
          try {
            // Generate signed URL with 60 minutes expiration for the image
            imageUrl = await this.gcsUploadService.generateSignedUrl(
              serviceCategory.imageFileName,
              'service-categories', // folder where service category images are stored
              60, // expiration in minutes
            );
          } catch (error) {
            console.warn(
              `Failed to generate signed URL for service category ${serviceCategory.id} image:`,
              error.message,
            );
            // Fallback to public URL if signed URL generation fails
            imageUrl = serviceCategory.imagePublicUrl || undefined;
          }
        }

        return {
          id: serviceCategory.id.toString(),
          name: serviceCategory.name,
          shortCode: serviceCategory.shortCode,
          slug: serviceCategory.slug,
          description: serviceCategory.description,
          status: serviceCategory.status,
          availableOnline: serviceCategory.availableOnline,
          position: serviceCategory.position,
          color: serviceCategory.color,
          servicesCount: servicesCountMap.get(serviceCategory.id) || 0,
          subServiceCategoriesCount:
            subServiceCategoriesCountMap.get(serviceCategory.id) || 0,
          parentName: parentInfoMap.get(serviceCategory.id),
          image: imageUrl,
        };
      }),
    );

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async checkNameAvailability(
    name: string,
    businessId: string | null,
    excludeId?: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const whereConditions = [
      eq(serviceCategories.businessId, businessId),
      ilike(serviceCategories.name, name),
      isNull(serviceCategories.deletedAt),
    ];

    if (excludeId) {
      whereConditions.push(sql`${serviceCategories.id} != ${excludeId}`);
    }

    const existingServiceCategory = await this.db
      .select()
      .from(serviceCategories)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    return { available: !existingServiceCategory };
  }

  async update(
    id: string,
    userId: string,
    businessId: string | null,
    updateServiceCategoryDto: UpdateServiceCategoryDto,
    imageFile?: Express.Multer.File,
    ogImageFile?: Express.Multer.File,
  ): Promise<ServiceCategoryDto> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if service category exists
      const existingServiceCategory = await this.db
        .select()
        .from(serviceCategories)
        .where(
          and(
            eq(serviceCategories.id, id),
            eq(serviceCategories.businessId, businessId),
            isNull(serviceCategories.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingServiceCategory) {
        throw new NotFoundException('Service category not found');
      }

      // Check name uniqueness if name is being updated
      if (
        updateServiceCategoryDto.name &&
        updateServiceCategoryDto.name !== existingServiceCategory.name
      ) {
        const existingName = await this.db
          .select()
          .from(serviceCategories)
          .where(
            and(
              eq(serviceCategories.businessId, businessId),
              ilike(serviceCategories.name, updateServiceCategoryDto.name),
              sql`${serviceCategories.id} != ${id}`,
              isNull(serviceCategories.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (existingName) {
          throw new ConflictException(
            `Service category with name "${updateServiceCategoryDto.name}" already exists`,
          );
        }
      }

      // Check shortCode uniqueness if shortCode is being updated
      if (updateServiceCategoryDto.shortCode) {
        const existingShortCode = await this.db
          .select()
          .from(serviceCategories)
          .where(
            and(
              eq(serviceCategories.businessId, businessId),
              ilike(
                serviceCategories.shortCode,
                updateServiceCategoryDto.shortCode,
              ),
              sql`${serviceCategories.id} != ${id}`,
              isNull(serviceCategories.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (existingShortCode) {
          throw new ConflictException(
            `Service category with short code "${updateServiceCategoryDto.shortCode}" already exists`,
          );
        }
      }

      // Check slug uniqueness if slug is being updated
      if (updateServiceCategoryDto.slug) {
        const existingSlug = await this.db
          .select()
          .from(serviceCategories)
          .where(
            and(
              eq(serviceCategories.businessId, businessId),
              ilike(serviceCategories.slug, updateServiceCategoryDto.slug),
              sql`${serviceCategories.id} != ${id}`,
              isNull(serviceCategories.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (existingSlug) {
          throw new ConflictException(
            `Service category with slug "${updateServiceCategoryDto.slug}" already exists`,
          );
        }
      }

      // Validate parent service category if provided
      if (updateServiceCategoryDto.parentId) {
        const parentServiceCategory = await this.db
          .select()
          .from(serviceCategories)
          .where(
            and(
              eq(serviceCategories.id, updateServiceCategoryDto.parentId),
              eq(serviceCategories.businessId, businessId),
              isNull(serviceCategories.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (!parentServiceCategory) {
          throw new BadRequestException('Parent service category not found');
        }

        // Prevent circular reference
        if (updateServiceCategoryDto.parentId === id) {
          throw new BadRequestException(
            'Service category cannot be its own parent',
          );
        }
      }

      // Update the service category
      await this.db
        .update(serviceCategories)
        .set({
          ...updateServiceCategoryDto,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(serviceCategories.id, id));

      // Handle image upload
      if (imageFile) {
        try {
          const imageMedia = await this.mediaService.uploadMediaWithReference(
            imageFile,
            MediaReferenceType.SERVICE_CATEGORIES_IMAGES,
            businessId,
            userId,
            id,
          );

          // Update service category with image reference
          await this.db
            .update(serviceCategories)
            .set({ image: imageMedia.id })
            .where(eq(serviceCategories.id, id));
        } catch (error) {
          console.warn(
            'Failed to upload service category image:',
            error.message,
          );
        }
      }

      // Handle OG image upload
      if (ogImageFile) {
        try {
          const ogImageMedia = await this.mediaService.uploadMediaWithReference(
            ogImageFile,
            MediaReferenceType.SERVICE_CATEGORIES_OG_IMAGES,
            businessId,
            userId,
            id,
          );

          // Update service category with OG image reference
          await this.db
            .update(serviceCategories)
            .set({ ogImage: ogImageMedia.id })
            .where(eq(serviceCategories.id, id));
        } catch (error) {
          console.warn('Failed to upload OG image:', error.message);
        }
      }

      // Handle location assignments
      if (updateServiceCategoryDto.locationIds !== undefined) {
        // Remove existing location associations
        await this.db
          .delete(serviceCategoryLocations)
          .where(eq(serviceCategoryLocations.serviceCategoryId, id));

        // Add new location associations if not allocated to all locations
        if (
          !updateServiceCategoryDto.isAllocatedToAllLocations &&
          updateServiceCategoryDto.locationIds &&
          updateServiceCategoryDto.locationIds.length > 0
        ) {
          // Validate location IDs
          const validLocations = await this.db
            .select({ id: locations.id })
            .from(locations)
            .where(
              and(
                eq(locations.businessId, businessId),
                inArray(locations.id, updateServiceCategoryDto.locationIds),
                isNull(locations.deletedAt),
              ),
            );

          if (
            validLocations.length !==
            updateServiceCategoryDto.locationIds.length
          ) {
            throw new BadRequestException(
              'One or more location IDs are invalid',
            );
          }

          // Insert new location associations
          const locationAssociations = updateServiceCategoryDto.locationIds.map(
            (locationId) => ({
              serviceCategoryId: id,
              locationId,
              createdBy: userId,
              updatedBy: userId,
            }),
          );

          await this.db
            .insert(serviceCategoryLocations)
            .values(locationAssociations);
        }
      }

      // Log activity
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        `Updated service category: ${updateServiceCategoryDto.name || existingServiceCategory.name}`,
        { id, type: 'service-category' },
        { id: userId, type: 'user' },
        { serviceCategoryId: id, businessId },
      );

      // Return updated service category
      return this.findOne(id, businessId);
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update service category: ${error.message}`,
      );
    }
  }

  async remove(
    id: string,
    userId: string,
    businessId: string | null,
  ): Promise<{ message: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if service category exists
      const existingServiceCategory = await this.db
        .select()
        .from(serviceCategories)
        .where(
          and(
            eq(serviceCategories.id, id),
            eq(serviceCategories.businessId, businessId),
            isNull(serviceCategories.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingServiceCategory) {
        throw new NotFoundException('Service category not found');
      }

      // Check if service category has associated services
      const servicesCount = await this.getServicesCountForServiceCategory(
        id,
        businessId,
      );

      if (servicesCount > 0) {
        throw new BadRequestException(
          `Cannot delete service category. It has ${servicesCount} associated service(s). Please remove or reassign the services first.`,
        );
      }

      // Check if service category has child service categories
      const childServiceCategories = await this.db
        .select({ id: serviceCategories.id })
        .from(serviceCategories)
        .where(
          and(
            eq(serviceCategories.parentId, id),
            eq(serviceCategories.businessId, businessId),
            isNull(serviceCategories.deletedAt),
          ),
        );

      if (childServiceCategories.length > 0) {
        throw new BadRequestException(
          `Cannot delete service category. It has ${childServiceCategories.length} child service category(s). Please remove or reassign the child service categories first.`,
        );
      }

      // Soft delete the service category
      await this.db
        .update(serviceCategories)
        .set({
          deletedBy: userId,
          deletedAt: new Date(),
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(serviceCategories.id, id));

      // Log activity
      await this.activityLogService.log(
        ActivityLogName.DELETE,
        `Deleted service category: ${existingServiceCategory.name}`,
        { id, type: 'service-category' },
        { id: userId, type: 'user' },
        { serviceCategoryId: id, businessId },
      );

      return { message: 'Service category deleted successfully' };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete service category: ${error.message}`,
      );
    }
  }

  async bulkDelete(
    ids: string[],
    userId: string,
    businessId: string | null,
  ): Promise<{ message: string; deletedCount: number }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!ids || ids.length === 0) {
        throw new BadRequestException('No service category IDs provided');
      }

      // Check if all service categories exist
      const existingServiceCategories = await this.db
        .select({ id: serviceCategories.id, name: serviceCategories.name })
        .from(serviceCategories)
        .where(
          and(
            inArray(serviceCategories.id, ids),
            eq(serviceCategories.businessId, businessId),
            isNull(serviceCategories.deletedAt),
          ),
        );

      if (existingServiceCategories.length !== ids.length) {
        throw new BadRequestException(
          'One or more service categories not found',
        );
      }

      // Check for associated services
      const servicesCountMap = await this.getServicesCountForServiceCategories(
        ids,
        businessId,
      );

      const serviceCategoriesWithServices = [];
      for (const [serviceCategoryId, count] of servicesCountMap.entries()) {
        if (count > 0) {
          const serviceCategory = existingServiceCategories.find(
            (st) => st.id === serviceCategoryId,
          );
          serviceCategoriesWithServices.push(
            `${serviceCategory?.name || serviceCategoryId} (${count} services)`,
          );
        }
      }

      if (serviceCategoriesWithServices.length > 0) {
        throw new BadRequestException(
          `Cannot delete service categories with associated services: ${serviceCategoriesWithServices.join(', ')}. Please remove or reassign the services first.`,
        );
      }

      // Check for child service categories
      const childServiceCategories = await this.db
        .select({
          parentId: serviceCategories.parentId,
          id: serviceCategories.id,
          name: serviceCategories.name,
        })
        .from(serviceCategories)
        .where(
          and(
            inArray(serviceCategories.parentId, ids),
            eq(serviceCategories.businessId, businessId),
            isNull(serviceCategories.deletedAt),
          ),
        );

      if (childServiceCategories.length > 0) {
        const parentNames = existingServiceCategories
          .filter((st) =>
            childServiceCategories.some((child) => child.parentId === st.id),
          )
          .map((st) => st.name);

        throw new BadRequestException(
          `Cannot delete service categories with child service categories: ${parentNames.join(', ')}. Please remove or reassign the child service categories first.`,
        );
      }

      // Bulk soft delete
      await this.db
        .update(serviceCategories)
        .set({
          deletedBy: userId,
          deletedAt: new Date(),
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(inArray(serviceCategories.id, ids));

      // Log activities
      for (const serviceCategory of existingServiceCategories) {
        await this.activityLogService.log(
          ActivityLogName.DELETE,
          `Deleted service category: ${serviceCategory.name}`,
          { id: serviceCategory.id, type: 'service-category' },
          { id: userId, type: 'user' },
          { serviceCategoryId: serviceCategory.id, businessId },
        );
      }

      return {
        message: 'Service categories deleted successfully',
        deletedCount: existingServiceCategories.length,
      };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete service categories: ${error.message}`,
      );
    }
  }

  async findAllSlim(
    businessId: string | null,
  ): Promise<ServiceCategorySlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Get all service categories with their media information
    const result = await this.db
      .select({
        id: serviceCategories.id,
        name: serviceCategories.name,
        position: serviceCategories.position,
        parentId: serviceCategories.parentId,
        image: serviceCategories.image,
      })
      .from(serviceCategories)
      .where(
        and(
          eq(serviceCategories.businessId, businessId),
          isNull(serviceCategories.deletedAt),
        ),
      )
      .orderBy(asc(serviceCategories.position));

    // Build hierarchy
    const serviceCategoryMap = new Map<string, ServiceCategorySlimDto>();
    const rootServiceCategories: ServiceCategorySlimDto[] = [];

    // First pass: create all service categories
    for (const serviceCategory of result) {
      const slimDto: ServiceCategorySlimDto = {
        id: serviceCategory.id,
        name: serviceCategory.name,
        position: serviceCategory.position,
        subServiceCategories: [],
      };

      // Handle media if exists
      if (serviceCategory.image) {
        try {
          const mediaData = await this.mediaService.findById(
            serviceCategory.image,
            businessId,
          );

          const signedUrl = await this.gcsUploadService.generateSignedUrl(
            mediaData.fileName,
            'service-categories',
            60,
          );

          slimDto.media = {
            id: mediaData.id,
            publicUrl: signedUrl,
            originalName: mediaData.originalName,
          };
        } catch (error) {
          console.warn(
            `Failed to get media for service category ${serviceCategory.id}:`,
            error.message,
          );
        }
      }

      serviceCategoryMap.set(serviceCategory.id, slimDto);
    }

    // Second pass: build hierarchy
    for (const serviceCategory of result) {
      const slimDto = serviceCategoryMap.get(serviceCategory.id);
      if (!slimDto) continue;

      if (serviceCategory.parentId) {
        const parent = serviceCategoryMap.get(serviceCategory.parentId);
        if (parent && parent.subServiceCategories) {
          parent.subServiceCategories.push(slimDto);
        }
      } else {
        rootServiceCategories.push(slimDto);
      }
    }

    return rootServiceCategories;
  }

  async findAllHierarchy(
    businessId: string | null,
  ): Promise<ServiceCategorySlimDto[]> {
    // For now, this is the same as findAllSlim
    // Can be extended later if different hierarchy logic is needed
    return this.findAllSlim(businessId);
  }

  async updatePositions(
    positionUpdates: { id: string; position: number }[],
    userId: string,
    businessId: string | null,
  ): Promise<void> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!positionUpdates || positionUpdates.length === 0) {
        throw new BadRequestException('No position updates provided');
      }

      // Validate all service categories exist and belong to the business
      const serviceCategoryIds = positionUpdates.map((update) => update.id);
      const existingServiceCategories = await this.db
        .select({ id: serviceCategories.id })
        .from(serviceCategories)
        .where(
          and(
            inArray(serviceCategories.id, serviceCategoryIds),
            eq(serviceCategories.businessId, businessId),
            isNull(serviceCategories.deletedAt),
          ),
        );

      if (existingServiceCategories.length !== serviceCategoryIds.length) {
        throw new BadRequestException(
          'One or more service categories not found',
        );
      }

      // Batch update positions
      const updatePromises = positionUpdates.map((update) =>
        this.db
          .update(serviceCategories)
          .set({
            position: update.position,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(serviceCategories.id, update.id)),
      );

      // Execute all updates in parallel for better performance
      await Promise.all(updatePromises);
    } catch (error) {
      console.warn('Failed to batch update positions:', error.message);
      throw error;
    }
  }

  private async getServicesCountForServiceCategory(
    serviceCategoryId: string,
    businessId: string,
  ): Promise<number> {
    try {
      const result = await this.db
        .select({ count: sql<number>`count(*)` })
        .from(services)
        .where(
          and(
            eq(services.businessId, businessId),
            eq(services.serviceCategoryId, serviceCategoryId),
            isNull(services.deletedAt),
          ),
        );

      return Number(result[0].count);
    } catch (error) {
      console.warn(
        `Failed to get services count for service category ${serviceCategoryId}:`,
        error.message,
      );
      return 0;
    }
  }

  private async getServicesCountForServiceCategories(
    serviceCategoryIds: string[],
    businessId: string,
  ): Promise<Map<string, number>> {
    try {
      if (serviceCategoryIds.length === 0) {
        return new Map();
      }

      const results = await this.db
        .select({
          serviceCategoryId: services.serviceCategoryId,
          count: sql<number>`count(*)`.as('count'),
        })
        .from(services)
        .where(
          and(
            eq(services.businessId, businessId),
            inArray(services.serviceCategoryId, serviceCategoryIds),
            isNull(services.deletedAt),
          ),
        )
        .groupBy(services.serviceCategoryId);

      const countsMap = new Map<string, number>();

      // Initialize all service category IDs with 0
      serviceCategoryIds.forEach((id) => countsMap.set(id, 0));

      // Add actual counts
      results.forEach((result) => {
        if (result.serviceCategoryId) {
          countsMap.set(result.serviceCategoryId, result.count);
        }
      });

      return countsMap;
    } catch (error) {
      console.warn(
        'Failed to get services count for service categories:',
        error.message,
      );
      return new Map();
    }
  }
}
