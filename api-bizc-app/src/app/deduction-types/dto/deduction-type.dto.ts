import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  DeductionCalculationMethod,
  DeductionAppliesTo,
} from '../../shared/types';

export class DeductionTypeDto {
  @ApiProperty({ description: 'Deduction type ID' })
  id: string;

  @ApiProperty({ description: 'Business ID' })
  businessId: string;

  @ApiProperty({ description: 'Deduction name' })
  deductionName: string;

  @ApiProperty({ description: 'Deduction code' })
  deductionCode: string;

  @ApiProperty({
    description: 'Calculation method for the deduction',
    enum: DeductionCalculationMethod,
    example: DeductionCalculationMethod.FIXED,
  })
  calculationMethod: DeductionCalculationMethod;

  @ApiPropertyOptional({ description: 'Deduction amount' })
  amount?: number;

  @ApiProperty({ description: 'Whether this deduction type is system defined' })
  isSystemDefined: boolean;

  @ApiProperty({ description: 'Whether this deduction is active' })
  isActive: boolean;

  @ApiProperty({
    description: 'What the deduction applies to',
    enum: DeductionAppliesTo,
    example: DeductionAppliesTo.GROSS,
  })
  appliesTo: DeductionAppliesTo;

  @ApiPropertyOptional({ description: 'Deduction type description' })
  description?: string;

  @ApiProperty({ description: 'Created by user name' })
  createdBy: string;

  @ApiPropertyOptional({ description: 'Updated by user name' })
  updatedBy?: string;

  @ApiProperty({ description: 'Creation date' })
  createdAt: Date;

  @ApiProperty({ description: 'Last update date' })
  updatedAt: Date;
}
