import { ApiProperty } from '@nestjs/swagger';
import {
  DeductionCalculationMethod,
  DeductionAppliesTo,
} from '../../shared/types';

export class DeductionTypeSlimDto {
  @ApiProperty({ description: 'Deduction type ID' })
  id: string;

  @ApiProperty({ description: 'Deduction name' })
  deductionName: string;

  @ApiProperty({ description: 'Deduction code' })
  deductionCode: string;

  @ApiProperty({
    description: 'Calculation method for the deduction',
    enum: DeductionCalculationMethod,
    example: DeductionCalculationMethod.FIXED,
  })
  calculationMethod: DeductionCalculationMethod;

  @ApiProperty({ description: 'Whether this deduction type is system defined' })
  isSystemDefined: boolean;

  @ApiProperty({ description: 'Whether this deduction is active' })
  isActive: boolean;

  @ApiProperty({
    description: 'What the deduction applies to',
    enum: DeductionAppliesTo,
    example: DeductionAppliesTo.GROSS,
  })
  appliesTo: DeductionAppliesTo;
}
