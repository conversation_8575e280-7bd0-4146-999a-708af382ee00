import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  IsBoolean,
  IsEnum,
  IsNumber,
} from 'class-validator';
import { Transform } from 'class-transformer';
import {
  DeductionCalculationMethod,
  DeductionAppliesTo,
} from '../../shared/types';

export class CreateDeductionTypeDto {
  @ApiProperty({ description: 'Deduction name', maxLength: 191 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(191)
  deductionName: string;

  @ApiProperty({ description: 'Deduction code', maxLength: 191 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(191)
  deductionCode: string;

  @ApiProperty({
    description: 'Calculation method for the deduction',
    enum: DeductionCalculationMethod,
    example: DeductionCalculationMethod.FIXED,
  })
  @IsNotEmpty()
  @IsEnum(DeductionCalculationMethod, {
    message: 'Calculation method must be a valid deduction calculation method',
  })
  calculationMethod: DeductionCalculationMethod;

  @ApiPropertyOptional({
    description: 'Deduction amount',
  })
  @IsOptional()
  @IsNumber()
  amount?: number;

  @ApiPropertyOptional({
    description: 'Whether this deduction type is system defined',
    default: false,
  })
  @IsBoolean({ message: 'Is system defined must be a boolean' })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  isSystemDefined?: boolean;

  @ApiPropertyOptional({
    description: 'Whether this deduction is active',
    default: true,
  })
  @IsBoolean({ message: 'Is active must be a boolean' })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'What the deduction applies to',
    enum: DeductionAppliesTo,
    default: DeductionAppliesTo.GROSS,
  })
  @IsOptional()
  @IsEnum(DeductionAppliesTo, {
    message: 'Applies to must be a valid deduction applies to value',
  })
  appliesTo?: DeductionAppliesTo;

  @ApiPropertyOptional({
    description: 'Deduction type description',
  })
  @IsOptional()
  @IsString()
  description?: string;
}
