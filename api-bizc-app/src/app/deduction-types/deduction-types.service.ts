import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
  ConflictException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateDeductionTypeDto } from './dto/create-deduction-type.dto';
import { UpdateDeductionTypeDto } from './dto/update-deduction-type.dto';
import { DeductionTypeDto } from './dto/deduction-type.dto';
import { DeductionTypeSlimDto } from './dto/deduction-type-slim.dto';
import { DeductionTypeListDto } from './dto/deduction-type-list.dto';
import { CreateEmployeeDeductionDto } from './dto/create-employee-deduction.dto';
import { EmployeeDeductionDto } from './dto/employee-deduction.dto';
import {
  deductionTypes,
  employeeDeductions,
} from '../drizzle/schema/deduction-types.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import {
  eq,
  and,
  or,
  isNull,
  ilike,
  sql,
  gte,
  lte,
  asc,
  desc,
  inArray,
} from 'drizzle-orm';
import { users } from '../drizzle/schema/users.schema';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { ActivityLogName } from '../shared/types';
import { UsersService } from '../users/users.service';

@Injectable()
export class DeductionTypesService {
  constructor(
    @Inject(DRIZZLE) private db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createDeductionTypeDto: CreateDeductionTypeDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      const existingDeductionType = await this.db
        .select()
        .from(deductionTypes)
        .where(
          and(
            eq(deductionTypes.businessId, businessId),
            ilike(
              deductionTypes.deductionCode,
              createDeductionTypeDto.deductionCode,
            ),
            isNull(deductionTypes.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (existingDeductionType) {
        throw new ConflictException(
          `A deduction type with the code '${createDeductionTypeDto.deductionCode}' already exists for this business`,
        );
      }

      const [newDeductionType] = await this.db
        .insert(deductionTypes)
        .values({
          businessId,
          deductionName: createDeductionTypeDto.deductionName,
          deductionCode: createDeductionTypeDto.deductionCode,
          calculationMethod: createDeductionTypeDto.calculationMethod,
          amount: createDeductionTypeDto.amount,
          isSystemDefined: createDeductionTypeDto.isSystemDefined ?? false,
          isActive: createDeductionTypeDto.isActive ?? true,
          appliesTo: createDeductionTypeDto.appliesTo,
          description: createDeductionTypeDto.description,
          createdBy: userId,
        })
        .returning();

      await this.activityLogService.log(
        ActivityLogName.CREATE,
        `Deduction type "${createDeductionTypeDto.deductionName}" was created`,
        { id: newDeductionType.id.toString(), type: 'deduction-type' },
        { id: userId, type: 'user' },
        { deductionTypeId: newDeductionType.id, businessId },
      );

      return {
        id: newDeductionType.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create deduction type: ${error.message}`,
      );
    }
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: DeductionTypeDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    const whereConditions = [
      isNull(deductionTypes.deletedAt),
      eq(deductionTypes.businessId, businessId),
    ];

    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(deductionTypes.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(deductionTypes.createdAt, toDate));
      }
    }

    const result = await this.db
      .select()
      .from(deductionTypes)
      .where(and(...whereConditions))
      .orderBy(asc(deductionTypes.deductionName))
      .limit(limit)
      .offset(offset);

    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(deductionTypes)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    await this.activityLogService.log(
      ActivityLogName.VIEW,
      'Viewed all deduction types',
      { id: businessId, type: 'business' },
      { id: userId, type: 'user' },
    );

    return {
      data: await Promise.all(
        result.map((deductionType) =>
          this.mapToDeductionTypeDto(deductionType),
        ),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    deductionName?: string,
    deductionCode?: string,
    calculationMethod?: string,
    isSystemDefined?: string,
    isActive?: string,
    appliesTo?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: DeductionTypeListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      isNull(deductionTypes.deletedAt),
      eq(deductionTypes.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(deductionTypes.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        toDate.setHours(23, 59, 59, 999);
        whereConditions.push(lte(deductionTypes.createdAt, toDate));
      }
    }

    // Add individual filter conditions
    if (deductionName) {
      whereConditions.push(
        ilike(deductionTypes.deductionName, `%${deductionName}%`),
      );
    }

    if (deductionCode) {
      whereConditions.push(
        ilike(deductionTypes.deductionCode, `%${deductionCode}%`),
      );
    }

    if (calculationMethod) {
      whereConditions.push(
        eq(deductionTypes.calculationMethod, calculationMethod as any),
      );
    }

    if (isSystemDefined) {
      const boolValue = isSystemDefined.toLowerCase() === 'true';
      whereConditions.push(eq(deductionTypes.isSystemDefined, boolValue));
    }

    if (isActive) {
      const boolValue = isActive.toLowerCase() === 'true';
      whereConditions.push(eq(deductionTypes.isActive, boolValue));
    }

    if (appliesTo) {
      whereConditions.push(eq(deductionTypes.appliesTo, appliesTo as any));
    }

    // Add advanced filters if provided
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          const { id: fieldId, value, operator } = filter;

          if (fieldId === 'deductionName') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(deductionTypes.deductionName, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(deductionTypes.deductionName, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(deductionTypes.deductionName, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${deductionTypes.deductionName} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${deductionTypes.deductionName} IS NULL OR ${deductionTypes.deductionName} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${deductionTypes.deductionName} IS NOT NULL AND ${deductionTypes.deductionName} != ''`,
                );
                break;
            }
          } else if (fieldId === 'deductionCode') {
            switch (operator) {
              case 'iLike':
                filterConditions.push(
                  ilike(deductionTypes.deductionCode, `%${value}%`),
                );
                break;
              case 'notILike':
                filterConditions.push(
                  sql`NOT ${ilike(deductionTypes.deductionCode, `%${value}%`)}`,
                );
                break;
              case 'eq':
                filterConditions.push(eq(deductionTypes.deductionCode, value));
                break;
              case 'ne':
                filterConditions.push(
                  sql`${deductionTypes.deductionCode} != ${value}`,
                );
                break;
              case 'isEmpty':
                filterConditions.push(
                  sql`${deductionTypes.deductionCode} IS NULL OR ${deductionTypes.deductionCode} = ''`,
                );
                break;
              case 'isNotEmpty':
                filterConditions.push(
                  sql`${deductionTypes.deductionCode} IS NOT NULL AND ${deductionTypes.deductionCode} != ''`,
                );
                break;
            }
          } else if (fieldId === 'calculationMethod') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(deductionTypes.calculationMethod, value),
                  );
                } else {
                  filterConditions.push(
                    eq(deductionTypes.calculationMethod, value),
                  );
                }
                break;
              case 'ne':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    sql`${deductionTypes.calculationMethod} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                  );
                } else {
                  filterConditions.push(
                    sql`${deductionTypes.calculationMethod} != ${value}`,
                  );
                }
                break;
            }
          } else if (fieldId === 'isSystemDefined') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  const boolValues = value.map(
                    (v) => v === 'true' || v === true,
                  );
                  if (boolValues.includes(true) && boolValues.includes(false)) {
                    // Both true and false, no filtering needed
                  } else if (boolValues.includes(true)) {
                    filterConditions.push(eq(deductionTypes.isSystemDefined, true));
                  } else if (boolValues.includes(false)) {
                    filterConditions.push(
                      eq(deductionTypes.isSystemDefined, false),
                    );
                  }
                } else {
                  const boolValue = value === 'true' || value === true;
                  filterConditions.push(
                    eq(deductionTypes.isSystemDefined, boolValue),
                  );
                }
                break;
              case 'ne': {
                const boolValue = value === 'true' || value === true;
                filterConditions.push(
                  eq(deductionTypes.isSystemDefined, !boolValue),
                );
                break;
              }
            }
          } else if (fieldId === 'isActive') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  const boolValues = value.map(
                    (v) => v === 'true' || v === true,
                  );
                  if (boolValues.includes(true) && boolValues.includes(false)) {
                    // Both true and false, no filtering needed
                  } else if (boolValues.includes(true)) {
                    filterConditions.push(eq(deductionTypes.isActive, true));
                  } else if (boolValues.includes(false)) {
                    filterConditions.push(eq(deductionTypes.isActive, false));
                  }
                } else {
                  const boolValue = value === 'true' || value === true;
                  filterConditions.push(eq(deductionTypes.isActive, boolValue));
                }
                break;
              case 'ne': {
                const boolValue = value === 'true' || value === true;
                filterConditions.push(eq(deductionTypes.isActive, !boolValue));
                break;
              }
            }
          } else if (fieldId === 'appliesTo') {
            switch (operator) {
              case 'eq':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    inArray(deductionTypes.appliesTo, value),
                  );
                } else {
                  filterConditions.push(eq(deductionTypes.appliesTo, value));
                }
                break;
              case 'ne':
                if (Array.isArray(value)) {
                  filterConditions.push(
                    sql`${deductionTypes.appliesTo} NOT IN (${value.map((s) => `'${s}'`).join(',')})`,
                  );
                } else {
                  filterConditions.push(
                    sql`${deductionTypes.appliesTo} != ${value}`,
                  );
                }
                break;
            }
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(and(...filterConditions));
          }
        }
      } catch {
        // Invalid JSON, ignore filters
      }
    }

    // Build sort conditions
    let orderBy = [asc(deductionTypes.deductionName), asc(deductionTypes.id)];

    if (sort) {
      try {
        const parsedSort = JSON.parse(sort);
        if (parsedSort.length > 0) {
          const sortField = parsedSort[0];
          const isDesc = sortField.desc === true;

          switch (sortField.id) {
            case 'deductionName':
              orderBy = [
                isDesc
                  ? desc(deductionTypes.deductionName)
                  : asc(deductionTypes.deductionName),
                asc(deductionTypes.id),
              ];
              break;
            case 'deductionCode':
              orderBy = [
                isDesc
                  ? desc(deductionTypes.deductionCode)
                  : asc(deductionTypes.deductionCode),
                asc(deductionTypes.id),
              ];
              break;
            case 'createdAt':
              orderBy = [
                isDesc
                  ? desc(deductionTypes.createdAt)
                  : asc(deductionTypes.createdAt),
                asc(deductionTypes.id),
              ];
              break;
            case 'deductionName':
              orderBy = [
                isDesc
                  ? desc(deductionTypes.deductionName)
                  : asc(deductionTypes.deductionName),
                asc(deductionTypes.id),
              ];
              break;
            default:
              // Keep default sorting
              break;
          }
        }
      } catch {
        // Invalid JSON, keep default sorting
      }
    }

    const result = await this.db
      .select()
      .from(deductionTypes)
      .where(and(...whereConditions))
      .orderBy(...orderBy)
      .limit(limit)
      .offset(offset);

    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(deductionTypes)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    await this.activityLogService.log(
      ActivityLogName.VIEW,
      'Viewed deduction types (optimized)',
      { id: businessId, type: 'business' },
      { id: userId, type: 'user' },
    );

    return {
      data: result.map((deductionType) =>
        this.mapToDeductionTypeListDto(deductionType),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async checkDeductionCodeAvailability(
    userId: string,
    businessId: string | null,
    deductionCode: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const existingDeductionType = await this.db
      .select()
      .from(deductionTypes)
      .where(
        and(
          eq(deductionTypes.businessId, businessId),
          ilike(deductionTypes.deductionCode, deductionCode),
          isNull(deductionTypes.deletedAt),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingDeductionType };
  }

  async findOne(userId: string, id: string): Promise<DeductionTypeDto> {
    const deductionType = await this.db
      .select()
      .from(deductionTypes)
      .where(and(eq(deductionTypes.id, id), isNull(deductionTypes.deletedAt)))
      .then((results) => results[0]);

    if (!deductionType) {
      throw new NotFoundException(`Deduction type with ID ${id} not found`);
    }

    const user = await this.db.query.users.findFirst({
      where: eq(users.id, userId),
    });

    if (
      !user ||
      !user.activeBusinessId ||
      user.activeBusinessId !== deductionType.businessId
    ) {
      throw new UnauthorizedException('Access denied to this deduction type');
    }

    await this.activityLogService.log(
      ActivityLogName.VIEW,
      `Viewed deduction type "${deductionType.deductionName}"`,
      { id: id, type: 'deduction-type' },
      { id: userId, type: 'user' },
    );

    return await this.mapToDeductionTypeDto(deductionType);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateDeductionTypeDto: UpdateDeductionTypeDto,
  ): Promise<{ id: string }> {
    const existingDeductionType = await this.db
      .select()
      .from(deductionTypes)
      .where(and(eq(deductionTypes.id, id), isNull(deductionTypes.deletedAt)))
      .then((results) => results[0]);

    if (!existingDeductionType) {
      throw new NotFoundException(`Deduction type with ID ${id} not found`);
    }

    if (businessId !== existingDeductionType.businessId) {
      throw new UnauthorizedException(
        'Access denied to update this deduction type',
      );
    }

    if (
      updateDeductionTypeDto.deductionCode &&
      updateDeductionTypeDto.deductionCode !==
        existingDeductionType.deductionCode
    ) {
      const codeConflict = await this.db
        .select()
        .from(deductionTypes)
        .where(
          and(
            eq(deductionTypes.businessId, businessId),
            ilike(
              deductionTypes.deductionCode,
              updateDeductionTypeDto.deductionCode,
            ),
            isNull(deductionTypes.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (codeConflict) {
        throw new ConflictException(
          `A deduction type with the code '${updateDeductionTypeDto.deductionCode}' already exists for this business`,
        );
      }
    }

    try {
      const [updatedDeductionType] = await this.db
        .update(deductionTypes)
        .set({
          ...updateDeductionTypeDto,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(deductionTypes.id, id))
        .returning();

      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        `Deduction type "${existingDeductionType.deductionName}" was updated`,
        { id: id, type: 'deduction-type' },
        { id: userId, type: 'user' },
        {
          deductionTypeId: id,
          businessId,
          changes: updateDeductionTypeDto,
          previousName: existingDeductionType.deductionName,
        },
      );

      return {
        id: updatedDeductionType.id,
      };
    } catch (error) {
      throw new BadRequestException(
        `Failed to update deduction type: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ success: boolean; message: string }> {
    const existingDeductionType = await this.db
      .select()
      .from(deductionTypes)
      .where(and(eq(deductionTypes.id, id), isNull(deductionTypes.deletedAt)))
      .then((results) => results[0]);

    if (!existingDeductionType) {
      throw new NotFoundException(`Deduction type with ID ${id} not found`);
    }

    if (businessId !== existingDeductionType.businessId) {
      throw new UnauthorizedException(
        'Access denied to delete this deduction type',
      );
    }

    await this.db
      .update(deductionTypes)
      .set({
        deletedBy: userId,
        deletedAt: new Date(),
        updatedAt: new Date(),
      })
      .where(eq(deductionTypes.id, id));

    await this.activityLogService.log(
      ActivityLogName.DELETE,
      `Deduction type "${existingDeductionType.deductionName}" was deleted`,
      { id: id, type: 'deduction-type' },
      { id: userId, type: 'user' },
      { deductionTypeId: id, businessId },
    );

    return {
      success: true,
      message: `Deduction type with ID ${id} has been deleted`,
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<DeductionTypeSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const deductionTypeResults = await this.db
      .select({
        id: deductionTypes.id,
        deductionName: deductionTypes.deductionName,
        deductionCode: deductionTypes.deductionCode,
        calculationMethod: deductionTypes.calculationMethod,
        isSystemDefined: deductionTypes.isSystemDefined,
        isActive: deductionTypes.isActive,
        appliesTo: deductionTypes.appliesTo,
      })
      .from(deductionTypes)
      .where(
        and(
          isNull(deductionTypes.deletedAt),
          eq(deductionTypes.businessId, businessId),
        ),
      )
      .orderBy(asc(deductionTypes.deductionName));

    await this.activityLogService.log(
      ActivityLogName.VIEW,
      'Viewed deduction type list (slim)',
      { id: businessId, type: 'business' },
      { id: userId, type: 'user' },
    );

    return deductionTypeResults.map((deductionType) => ({
      id: deductionType.id.toString(),
      deductionName: deductionType.deductionName,
      deductionCode: deductionType.deductionCode,
      calculationMethod: deductionType.calculationMethod,
      isSystemDefined: deductionType.isSystemDefined,
      isActive: deductionType.isActive,
      appliesTo: deductionType.appliesTo,
    }));
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createDeductionTypeDto: CreateDeductionTypeDto,
  ): Promise<{ id: string }> {
    const deductionType = await this.create(
      userId,
      businessId,
      createDeductionTypeDto,
    );
    return { id: deductionType.id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateDeductionTypeDto: UpdateDeductionTypeDto,
  ): Promise<{ id: string }> {
    await this.update(userId, businessId, id, updateDeductionTypeDto);
    return { id };
  }

  // Employee Deductions methods
  async createEmployeeDeduction(
    userId: string,
    businessId: string | null,
    createEmployeeDeductionDto: CreateEmployeeDeductionDto,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Verify deduction type exists and belongs to business
      const deductionType = await this.db
        .select()
        .from(deductionTypes)
        .where(
          and(
            eq(deductionTypes.id, createEmployeeDeductionDto.deductionTypeId),
            eq(deductionTypes.businessId, businessId),
            isNull(deductionTypes.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!deductionType) {
        throw new NotFoundException('Deduction type not found');
      }

      // Verify employee exists and belongs to business
      const employee = await this.db
        .select()
        .from(staffMembers)
        .where(
          and(
            eq(staffMembers.id, createEmployeeDeductionDto.employeeId),
            eq(staffMembers.businessId, businessId),
            isNull(staffMembers.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!employee) {
        throw new NotFoundException('Employee not found');
      }

      const [newEmployeeDeduction] = await this.db
        .insert(employeeDeductions)
        .values({
          businessId,
          employeeId: createEmployeeDeductionDto.employeeId,
          deductionTypeId: createEmployeeDeductionDto.deductionTypeId,
          amount: createEmployeeDeductionDto.amount,
          percentage: createEmployeeDeductionDto.percentage,
          referenceValue: createEmployeeDeductionDto.referenceValue,
          limitAmount: createEmployeeDeductionDto.limitAmount,
          effectiveDate: createEmployeeDeductionDto.effectiveDate,
          endDate: createEmployeeDeductionDto.endDate,
          status: createEmployeeDeductionDto.status,
          totalAmount: createEmployeeDeductionDto.totalAmount,
          approvedBy: createEmployeeDeductionDto.approvedBy,
          approvedDate: createEmployeeDeductionDto.approvedBy
            ? new Date()
            : null,
          remarks: createEmployeeDeductionDto.remarks,
          createdBy: userId,
        })
        .returning();

      await this.activityLogService.log(
        ActivityLogName.CREATE,
        `Employee deduction created for "${employee.firstName} ${employee.lastName}"`,
        { id: newEmployeeDeduction.id.toString(), type: 'employee-deduction' },
        { id: userId, type: 'user' },
        { employeeDeductionId: newEmployeeDeduction.id, businessId },
      );

      return {
        id: newEmployeeDeduction.id,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof NotFoundException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create employee deduction: ${error.message}`,
      );
    }
  }

  async findAllEmployeeDeductions(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    employeeId?: string,
    deductionTypeId?: string,
  ): Promise<{
    data: EmployeeDeductionDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    const whereConditions = [
      isNull(employeeDeductions.deletedAt),
      eq(employeeDeductions.businessId, businessId),
    ];

    if (employeeId) {
      whereConditions.push(eq(employeeDeductions.employeeId, employeeId));
    }

    if (deductionTypeId) {
      whereConditions.push(
        eq(employeeDeductions.deductionTypeId, deductionTypeId),
      );
    }

    const result = await this.db
      .select()
      .from(employeeDeductions)
      .leftJoin(
        staffMembers,
        eq(employeeDeductions.employeeId, staffMembers.id),
      )
      .leftJoin(
        deductionTypes,
        eq(employeeDeductions.deductionTypeId, deductionTypes.id),
      )
      .where(and(...whereConditions))
      .orderBy(asc(employeeDeductions.effectiveDate))
      .limit(limit)
      .offset(offset);

    const totalResult = await this.db
      .select({ count: sql<number>`count(*)` })
      .from(employeeDeductions)
      .where(and(...whereConditions));

    const total = Number(totalResult[0].count);
    const totalPages = Math.ceil(total / limit);

    await this.activityLogService.log(
      ActivityLogName.VIEW,
      'Viewed employee deductions',
      { id: businessId, type: 'business' },
      { id: userId, type: 'user' },
    );

    return {
      data: await Promise.all(
        result.map((row) =>
          this.mapToEmployeeDeductionDto(
            row.employee_deductions,
            row.staff_members,
            row.deduction_types,
          ),
        ),
      ),
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  private async mapToDeductionTypeDto(
    deductionType: typeof deductionTypes.$inferSelect,
  ): Promise<DeductionTypeDto> {
    const createdByName = await this.usersService.getUserName(
      deductionType.createdBy.toString(),
    );
    let updatedByName: string | undefined;
    if (deductionType.updatedBy) {
      updatedByName = await this.usersService.getUserName(
        deductionType.updatedBy.toString(),
      );
    }

    const deductionTypeDto: DeductionTypeDto = {
      id: deductionType.id.toString(),
      businessId: deductionType.businessId.toString(),
      deductionName: deductionType.deductionName,
      deductionCode: deductionType.deductionCode,
      calculationMethod: deductionType.calculationMethod,
      amount: deductionType.amount ? Number(deductionType.amount) : undefined,
      isSystemDefined: deductionType.isSystemDefined,
      isActive: deductionType.isActive,
      appliesTo: deductionType.appliesTo,
      description: deductionType.description,
      createdBy: createdByName,
      updatedBy: updatedByName,
      createdAt: deductionType.createdAt,
      updatedAt: deductionType.updatedAt,
    };

    return deductionTypeDto;
  }

  private mapToDeductionTypeListDto(
    deductionType: typeof deductionTypes.$inferSelect,
  ): DeductionTypeListDto {
    return {
      id: deductionType.id.toString(),
      deductionName: deductionType.deductionName,
      deductionCode: deductionType.deductionCode,
      calculationMethod: deductionType.calculationMethod,
      amount: deductionType.amount ? Number(deductionType.amount) : undefined,
      isSystemDefined: deductionType.isSystemDefined,
      isActive: deductionType.isActive,
      appliesTo: deductionType.appliesTo,
      description: deductionType.description,
      createdAt: deductionType.createdAt,
      updatedAt: deductionType.updatedAt,
    };
  }

  private async mapToEmployeeDeductionDto(
    employeeDeduction: typeof employeeDeductions.$inferSelect,
    employee: typeof staffMembers.$inferSelect | null,
    deductionType: typeof deductionTypes.$inferSelect | null,
  ): Promise<EmployeeDeductionDto> {
    const createdByName = await this.usersService.getUserName(
      employeeDeduction.createdBy.toString(),
    );
    let updatedByName: string | undefined;
    if (employeeDeduction.updatedBy) {
      updatedByName = await this.usersService.getUserName(
        employeeDeduction.updatedBy.toString(),
      );
    }

    let approvedByName: string | undefined;
    if (employeeDeduction.approvedBy) {
      const approver = await this.db
        .select({
          firstName: staffMembers.firstName,
          lastName: staffMembers.lastName,
        })
        .from(staffMembers)
        .where(eq(staffMembers.id, employeeDeduction.approvedBy))
        .then((results) => results[0]);

      if (approver) {
        approvedByName = `${approver.firstName} ${approver.lastName}`;
      }
    }

    const employeeDeductionDto: EmployeeDeductionDto = {
      id: employeeDeduction.id.toString(),
      businessId: employeeDeduction.businessId.toString(),
      employeeId: employeeDeduction.employeeId.toString(),
      employeeName: employee
        ? `${employee.firstName} ${employee.lastName}`
        : 'Unknown',
      deductionTypeId: employeeDeduction.deductionTypeId.toString(),
      deductionTypeName: deductionType?.deductionName || 'Unknown',
      deductionTypeCode: deductionType?.deductionCode || 'Unknown',
      amount: employeeDeduction.amount,
      percentage: employeeDeduction.percentage,
      referenceValue: employeeDeduction.referenceValue,
      limitAmount: employeeDeduction.limitAmount,
      effectiveDate: employeeDeduction.effectiveDate,
      endDate: employeeDeduction.endDate,
      status: employeeDeduction.status,
      totalAmount: employeeDeduction.totalAmount,
      deductedAmount: employeeDeduction.deductedAmount,
      approvedBy: approvedByName,
      approvedDate: employeeDeduction.approvedDate,
      remarks: employeeDeduction.remarks,
      createdBy: createdByName,
      updatedBy: updatedByName,
      createdAt: employeeDeduction.createdAt,
      updatedAt: employeeDeduction.updatedAt,
    };

    return employeeDeductionDto;
  }

  // Bulk operations
  async bulkCreate(
    userId: string,
    businessId: string | null,
    createDeductionTypesDto: CreateDeductionTypeDto[],
  ): Promise<DeductionTypeDto[]> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!createDeductionTypesDto || createDeductionTypesDto.length === 0) {
        throw new BadRequestException(
          'No deduction types provided for creation',
        );
      }

      // Check for duplicate deduction codes within the request
      const deductionCodes = createDeductionTypesDto.map((dto) =>
        dto.deductionCode.toLowerCase(),
      );
      const duplicateCodes = deductionCodes.filter(
        (code, index) => deductionCodes.indexOf(code) !== index,
      );
      if (duplicateCodes.length > 0) {
        throw new ConflictException(
          `Duplicate deduction codes found in request: ${duplicateCodes.join(', ')}`,
        );
      }

      // Check for existing deduction codes in the database
      const existingDeductionTypes = await this.db
        .select({ deductionCode: deductionTypes.deductionCode })
        .from(deductionTypes)
        .where(
          and(
            eq(deductionTypes.businessId, businessId),
            isNull(deductionTypes.deletedAt),
          ),
        );

      const existingCodes = existingDeductionTypes.map((dt) =>
        dt.deductionCode.toLowerCase(),
      );
      const conflictingCodes = deductionCodes.filter((code) =>
        existingCodes.includes(code),
      );

      if (conflictingCodes.length > 0) {
        throw new ConflictException(
          `Deduction type codes already exist: ${conflictingCodes.join(', ')}`,
        );
      }

      const createdDeductionTypes: DeductionTypeDto[] = [];

      // Use transaction to ensure all creations succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const createDeductionTypeDto of createDeductionTypesDto) {
          const newDeductionType = await tx
            .insert(deductionTypes)
            .values({
              businessId,
              deductionName: createDeductionTypeDto.deductionName,
              deductionCode: createDeductionTypeDto.deductionCode,
              calculationMethod: createDeductionTypeDto.calculationMethod,
              amount: createDeductionTypeDto.amount,
              isSystemDefined: createDeductionTypeDto.isSystemDefined ?? false,
              isActive: createDeductionTypeDto.isActive ?? true,
              appliesTo: createDeductionTypeDto.appliesTo,
              description: createDeductionTypeDto.description,
              createdBy: userId,
              updatedAt: new Date(),
            })
            .returning();

          // Log the deduction type creation activity
          await this.activityLogService.log(
            ActivityLogName.CREATE,
            `Deduction type "${createDeductionTypeDto.deductionName}" was created (bulk)`,
            { id: newDeductionType[0].id.toString(), type: 'deduction-type' },
            { id: userId, type: 'user' },
            { deductionTypeId: newDeductionType[0].id, businessId },
          );

          createdDeductionTypes.push(
            await this.mapToDeductionTypeDto(newDeductionType[0]),
          );
        }
      });

      return createdDeductionTypes;
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof ConflictException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk create deduction types: ${error.message}`,
      );
    }
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createDeductionTypesDto: CreateDeductionTypeDto[],
  ): Promise<{ ids: string[] }> {
    const deductionTypes = await this.bulkCreate(
      userId,
      businessId,
      createDeductionTypesDto,
    );
    return { ids: deductionTypes.map((deductionType) => deductionType.id) };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    deductionTypeIds: string[],
  ): Promise<{
    deleted: number;
    message: string;
    deletedIds: string[];
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!deductionTypeIds || deductionTypeIds.length === 0) {
        throw new BadRequestException(
          'No deduction type IDs provided for deletion',
        );
      }

      // Get all deduction types that exist and belong to the business
      const existingDeductionTypes = await this.db
        .select({
          id: deductionTypes.id,
          deductionName: deductionTypes.deductionName,
          businessId: deductionTypes.businessId,
        })
        .from(deductionTypes)
        .where(
          and(
            inArray(deductionTypes.id, deductionTypeIds),
            eq(deductionTypes.businessId, businessId),
            isNull(deductionTypes.deletedAt),
          ),
        );

      if (existingDeductionTypes.length === 0) {
        throw new NotFoundException(
          'No valid deduction types found for deletion',
        );
      }

      // Check for employee deductions that would prevent deletion
      const deductionTypesWithEmployeeDeductions = await this.db
        .select({ deductionTypeId: employeeDeductions.deductionTypeId })
        .from(employeeDeductions)
        .where(
          and(
            inArray(
              employeeDeductions.deductionTypeId,
              existingDeductionTypes.map((dt) => dt.id),
            ),
            isNull(employeeDeductions.deletedAt),
          ),
        );

      if (deductionTypesWithEmployeeDeductions.length > 0) {
        const conflictingIds = deductionTypesWithEmployeeDeductions.map(
          (ed) => ed.deductionTypeId,
        );
        const conflictingNames = existingDeductionTypes
          .filter((dt) => conflictingIds.includes(dt.id))
          .map((dt) => dt.deductionName);

        throw new BadRequestException(
          `Cannot delete deduction types with existing employee deductions: ${conflictingNames.join(', ')}`,
        );
      }

      const deletedIds: string[] = [];
      const currentTime = new Date();

      // Use transaction to ensure all deletions succeed or fail together
      await this.db.transaction(async (tx) => {
        for (const deductionType of existingDeductionTypes) {
          // Soft delete the deduction type
          await tx
            .update(deductionTypes)
            .set({
              deletedBy: userId,
              deletedAt: currentTime,
              updatedAt: currentTime,
            })
            .where(eq(deductionTypes.id, deductionType.id));

          deletedIds.push(deductionType.id);

          // Log the activity for each deleted deduction type
          await this.activityLogService.log(
            ActivityLogName.DELETE,
            `Deduction type "${deductionType.deductionName}" was deleted (bulk)`,
            { id: deductionType.id, type: 'deduction-type' },
            { id: userId, type: 'user' },
            { deductionTypeId: deductionType.id, businessId },
          );
        }
      });

      return {
        deleted: deletedIds.length,
        message: `Successfully deleted ${deletedIds.length} deduction types`,
        deletedIds,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to bulk delete deduction types: ${error.message}`,
      );
    }
  }

  async bulkUpdateDeductionTypeStatus(
    userId: string,
    businessId: string | null,
    deductionTypeIds: string[],
    isActive: boolean,
  ): Promise<{
    updated: number;
    updatedIds: string[];
    failed: Array<{ deductionTypeId: string; error: string }>;
  }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      if (!deductionTypeIds || deductionTypeIds.length === 0) {
        throw new BadRequestException(
          'No deduction type IDs provided for status update',
        );
      }

      let updatedCount = 0;
      const updatedIds: string[] = [];
      const failed: Array<{ deductionTypeId: string; error: string }> = [];

      // Process each deduction type ID
      await this.db.transaction(async (tx) => {
        for (const deductionTypeId of deductionTypeIds) {
          try {
            // Check if deduction type exists and belongs to the business
            const existingDeductionType = await tx
              .select()
              .from(deductionTypes)
              .where(
                and(
                  eq(deductionTypes.id, deductionTypeId),
                  eq(deductionTypes.businessId, businessId),
                  isNull(deductionTypes.deletedAt),
                ),
              )
              .then((results) => results[0]);

            if (!existingDeductionType) {
              failed.push({
                deductionTypeId,
                error: 'Deduction type not found or access denied',
              });
              continue;
            }

            // Skip if status is already the same
            if (existingDeductionType.isActive === isActive) {
              failed.push({
                deductionTypeId,
                error: `Deduction type already has isActive: ${isActive}`,
              });
              continue;
            }

            // Update the deduction type status
            await tx
              .update(deductionTypes)
              .set({
                isActive,
                updatedAt: new Date(),
              })
              .where(
                and(
                  eq(deductionTypes.id, deductionTypeId),
                  eq(deductionTypes.businessId, businessId),
                  isNull(deductionTypes.deletedAt),
                ),
              );

            updatedCount++;
            updatedIds.push(deductionTypeId);

            // Log the status update activity
            await this.activityLogService.log(
              ActivityLogName.UPDATE,
              `Deduction type status updated to: ${isActive ? 'active' : 'inactive'}`,
              { id: deductionTypeId, type: 'deduction-type' },
              { id: userId, type: 'user' },
              {
                deductionTypeId,
                businessId,
                newStatus: isActive,
                oldStatus: existingDeductionType.isActive,
              },
            );
          } catch (error) {
            failed.push({
              deductionTypeId,
              error: `Failed to update: ${error.message}`,
            });
          }
        }
      });

      return {
        updated: updatedCount,
        updatedIds,
        failed,
      };
    } catch (error) {
      if (
        error instanceof UnauthorizedException ||
        error instanceof BadRequestException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update deduction type status: ${error.message}`,
      );
    }
  }
}
