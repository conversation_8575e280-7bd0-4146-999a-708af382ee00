import {
  BadRequestException,
  Injectable,
  Inject,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';
import { CreateCommentDto } from './dto/create-comment.dto';
import { UpdateCommentDto } from './dto/update-comment.dto';
import { CommentDto } from './dto/comment.dto';
import { CommentListDto } from './dto/comment-list.dto';
import { comments } from '../drizzle/schema/comments.schema';
import { staffMembers } from '../drizzle/schema/staff.schema';
import { media, MediaReferenceType } from '../drizzle/schema/media.schema';
import { and, eq, isNull, desc, asc, count, sql, ilike } from 'drizzle-orm';
import { StatusType } from '../shared/types';
import { MediaService } from '../media/media.service';

@Injectable()
export class CommentsService {
  constructor(
    @Inject(DRIZZLE) private readonly db: DrizzleDB,
    private readonly mediaService: MediaService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createCommentDto: CreateCommentDto,
    attachmentFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // If authorId is not provided, try to find the staff member for the current user
      let authorId = createCommentDto.authorId;
      if (!authorId) {
        const staffMember = await this.db
          .select({ id: staffMembers.id })
          .from(staffMembers)
          .where(
            and(
              eq(staffMembers.businessId, businessId),
              eq(staffMembers.userId, userId),
              isNull(staffMembers.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (!staffMember) {
          throw new BadRequestException(
            'No staff member found for this user in the current business',
          );
        }
        authorId = staffMember.id;
      }

      // Validate parent comment exists if parentId is provided
      if (createCommentDto.parentId) {
        const parentComment = await this.db
          .select({ id: comments.id })
          .from(comments)
          .where(
            and(
              eq(comments.id, createCommentDto.parentId),
              eq(comments.businessId, businessId),
              isNull(comments.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (!parentComment) {
          throw new BadRequestException('Parent comment not found');
        }
      }

      // Handle attachment upload if provided
      let attachmentId: string | undefined;
      if (attachmentFile) {
        const uploadResult = await this.mediaService.uploadMediaWithReference(
          attachmentFile,
          MediaReferenceType.COMMENTS,
          businessId,
          userId,
          null, // No specific reference ID for comment attachments
        );
        attachmentId = uploadResult.id;
      }

      // Create the comment
      const newComment = await this.db
        .insert(comments)
        .values({
          businessId,
          referenceId: createCommentDto.referenceId,
          parentId: createCommentDto.parentId || null,
          content: createCommentDto.content,
          attachmentId: attachmentId || null,
          authorId,
          status: StatusType.ACTIVE,
          createdBy: userId,
          updatedBy: userId,
        })
        .returning({ id: comments.id })
        .then((results) => results[0]);

      return { id: newComment.id };
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to create comment: ${error.message}`,
      );
    }
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    referenceId?: string,
    authorId?: string,
    parentId?: string,
    content?: string,
    status?: string,
  ): Promise<{
    data: CommentListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      isNull(comments.deletedAt),
      eq(comments.businessId, businessId),
    ];

    // Add filters
    if (referenceId) {
      whereConditions.push(eq(comments.referenceId, referenceId));
    }

    if (authorId) {
      whereConditions.push(eq(comments.authorId, authorId));
    }

    if (parentId) {
      whereConditions.push(eq(comments.parentId, parentId));
    }

    if (content) {
      whereConditions.push(ilike(comments.content, `%${content}%`));
    }

    if (status && Object.values(StatusType).includes(status as StatusType)) {
      whereConditions.push(eq(comments.status, status as StatusType));
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(comments)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    const total = totalResult?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Get comments with author information
    const commentsResult = await this.db
      .select({
        id: comments.id,
        referenceId: comments.referenceId,
        parentId: comments.parentId,
        content: comments.content,
        attachmentId: comments.attachmentId,
        authorId: comments.authorId,
        status: comments.status,
        createdAt: comments.createdAt,
        updatedAt: comments.updatedAt,
        // Author information
        authorName: sql<string>`CONCAT(${staffMembers.firstName}, ' ', ${staffMembers.lastName})`,
        authorProfileImageId: staffMembers.profileImageId,
      })
      .from(comments)
      .leftJoin(staffMembers, eq(comments.authorId, staffMembers.id))
      .where(and(...whereConditions))
      .orderBy(desc(comments.createdAt))
      .limit(limit)
      .offset(offset);

    // Get replies count for each comment
    const commentIds = commentsResult.map((comment) => comment.id);
    const repliesCounts =
      commentIds.length > 0
        ? await this.db
            .select({
              parentId: comments.parentId,
              count: count(),
            })
            .from(comments)
            .where(
              and(
                sql`${comments.parentId} IN (${sql.join(
                  commentIds.map((id) => sql`${id}`),
                  sql`, `,
                )})`,
                isNull(comments.deletedAt),
              ),
            )
            .groupBy(comments.parentId)
        : [];

    const repliesCountMap = new Map(
      repliesCounts.map((item) => [item.parentId, item.count]),
    );

    // Process results and generate signed URLs
    const data: CommentListDto[] = await Promise.all(
      commentsResult.map(async (comment) => {
        let attachmentUrl: string | undefined;
        let authorProfileImageUrl: string | undefined;

        // Generate signed URL for attachment
        if (comment.attachmentId) {
          try {
            attachmentUrl = await this.mediaService.generateSignedUrlForMedia(
              comment.attachmentId,
              businessId,
              MediaReferenceType.COMMENTS,
            );
          } catch (error) {
            console.warn(
              `Failed to generate signed URL for attachment ${comment.attachmentId}:`,
              error,
            );
          }
        }

        // Generate signed URL for author profile image
        if (comment.authorProfileImageId) {
          try {
            authorProfileImageUrl =
              await this.mediaService.generateSignedUrlForMedia(
                comment.authorProfileImageId,
                businessId,
                'profiles',
              );
          } catch (error) {
            console.warn(
              `Failed to generate signed URL for profile image ${comment.authorProfileImageId}:`,
              error,
            );
          }
        }

        return {
          id: comment.id,
          referenceId: comment.referenceId,
          parentId: comment.parentId,
          content: comment.content,
          attachmentUrl,
          authorId: comment.authorId,
          authorName: comment.authorName,
          authorProfileImageUrl,
          status: comment.status,
          repliesCount: repliesCountMap.get(comment.id) || 0,
          createdAt: comment.createdAt,
          updatedAt: comment.updatedAt,
        };
      }),
    );

    return {
      data,
      meta: {
        total,
        page,
        totalPages,
      },
    };
  }

  async findOne(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<CommentDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const commentResult = await this.db
      .select({
        id: comments.id,
        businessId: comments.businessId,
        referenceId: comments.referenceId,
        parentId: comments.parentId,
        content: comments.content,
        attachmentId: comments.attachmentId,
        authorId: comments.authorId,
        status: comments.status,
        createdBy: comments.createdBy,
        updatedBy: comments.updatedBy,
        createdAt: comments.createdAt,
        updatedAt: comments.updatedAt,
        // Author information
        authorName: sql<string>`CONCAT(${staffMembers.firstName}, ' ', ${staffMembers.lastName})`,
        authorProfileImageId: staffMembers.profileImageId,
      })
      .from(comments)
      .leftJoin(staffMembers, eq(comments.authorId, staffMembers.id))
      .where(
        and(
          eq(comments.id, id),
          eq(comments.businessId, businessId),
          isNull(comments.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!commentResult) {
      throw new NotFoundException('Comment not found');
    }

    // Get replies count
    const repliesCountResult = await this.db
      .select({ count: count() })
      .from(comments)
      .where(and(eq(comments.parentId, id), isNull(comments.deletedAt)))
      .then((results) => results[0]);

    const repliesCount = repliesCountResult?.count || 0;

    // Generate signed URLs
    let attachmentUrl: string | undefined;
    let authorProfileImageUrl: string | undefined;

    if (commentResult.attachmentId) {
      try {
        attachmentUrl = await this.mediaService.generateSignedUrlForMedia(
          commentResult.attachmentId,
          businessId,
          MediaReferenceType.COMMENTS,
        );
      } catch (error) {
        console.warn(
          `Failed to generate signed URL for attachment ${commentResult.attachmentId}:`,
          error,
        );
      }
    }

    if (commentResult.authorProfileImageId) {
      try {
        authorProfileImageUrl =
          await this.mediaService.generateSignedUrlForMedia(
            commentResult.authorProfileImageId,
            businessId,
            'profiles',
          );
      } catch (error) {
        console.warn(
          `Failed to generate signed URL for profile image ${commentResult.authorProfileImageId}:`,
          error,
        );
      }
    }

    return {
      id: commentResult.id,
      businessId: commentResult.businessId,
      referenceId: commentResult.referenceId,
      parentId: commentResult.parentId,
      content: commentResult.content,
      attachmentId: commentResult.attachmentId,
      attachmentUrl,
      authorId: commentResult.authorId,
      authorName: commentResult.authorName,
      authorProfileImageUrl,
      status: commentResult.status,
      repliesCount,
      createdBy: commentResult.createdBy,
      updatedBy: commentResult.updatedBy,
      createdAt: commentResult.createdAt,
      updatedAt: commentResult.updatedAt,
    };
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateCommentDto: UpdateCommentDto,
    attachmentFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if comment exists and belongs to the business
      const existingComment = await this.db
        .select({
          id: comments.id,
          attachmentId: comments.attachmentId,
          authorId: comments.authorId,
        })
        .from(comments)
        .where(
          and(
            eq(comments.id, id),
            eq(comments.businessId, businessId),
            isNull(comments.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingComment) {
        throw new NotFoundException('Comment not found');
      }

      // Handle attachment upload if provided
      let attachmentId: string | undefined = existingComment.attachmentId;
      if (attachmentFile) {
        // Delete old attachment if exists
        if (existingComment.attachmentId) {
          try {
            await this.mediaService.deleteMedia(
              existingComment.attachmentId,
              businessId,
              MediaReferenceType.COMMENTS,
            );
          } catch (error) {
            console.warn(
              `Failed to delete old attachment ${existingComment.attachmentId}:`,
              error,
            );
          }
        }

        // Upload new attachment
        const uploadResult = await this.mediaService.uploadMediaWithReference(
          attachmentFile,
          MediaReferenceType.COMMENTS,
          businessId,
          userId,
          null,
        );
        attachmentId = uploadResult.id;
      }

      // Update the comment
      const updateData: any = {
        updatedBy: userId,
      };

      if (updateCommentDto.content !== undefined) {
        updateData.content = updateCommentDto.content;
      }

      if (attachmentFile) {
        updateData.attachmentId = attachmentId;
      }

      await this.db.update(comments).set(updateData).where(eq(comments.id, id));

      return { id };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to update comment: ${error.message}`,
      );
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<{ message: string; id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if comment exists and belongs to the business
      const existingComment = await this.db
        .select({
          id: comments.id,
          attachmentId: comments.attachmentId,
        })
        .from(comments)
        .where(
          and(
            eq(comments.id, id),
            eq(comments.businessId, businessId),
            isNull(comments.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingComment) {
        throw new NotFoundException('Comment not found');
      }

      // Soft delete the comment
      await this.db
        .update(comments)
        .set({
          deletedBy: userId,
          deletedAt: new Date(),
        })
        .where(eq(comments.id, id));

      // Also soft delete all replies
      await this.db
        .update(comments)
        .set({
          deletedBy: userId,
          deletedAt: new Date(),
        })
        .where(
          and(
            eq(comments.parentId, id),
            eq(comments.businessId, businessId),
            isNull(comments.deletedAt),
          ),
        );

      return {
        message: 'Comment deleted successfully',
        id,
      };
    } catch (error) {
      if (
        error instanceof NotFoundException ||
        error instanceof UnauthorizedException
      ) {
        throw error;
      }
      throw new BadRequestException(
        `Failed to delete comment: ${error.message}`,
      );
    }
  }
}
