import {
  Injectable,
  ConflictException,
  NotFoundException,
  UnauthorizedException,
  BadRequestException,
  Inject,
} from '@nestjs/common';
import {
  and,
  eq,
  isNull,
  ilike,
  gte,
  lte,
  or,
  desc,
  asc,
  inArray,
  count,
  ne,
} from 'drizzle-orm';
import { games } from '../drizzle/schema/games.schema';
import { media, MediaReferenceType } from '../drizzle/schema/media.schema';
import { CreateGameDto } from './dto/create-game.dto';
import { UpdateGameDto } from './dto/update-game.dto';
import { GameDto } from './dto/game.dto';
import { GameListDto } from './dto/game-list.dto';
import { GameSlimDto } from './dto/game-slim.dto';
import { BulkGameIdsResponseDto } from './dto/bulk-game-ids-response.dto';
import { BulkDeleteGameResponseDto } from './dto/bulk-delete-game-response.dto';
import { DeleteGameResponseDto } from './dto/delete-game-response.dto';
import { GameStatus } from '../shared/types';
import { ActivityLogService } from '../activity-log/activity-log.service';
import { ActivityLogName } from '../shared/types/activity.enum';
import { MediaService } from '../media/media.service';
import { UsersService } from '../users/users.service';
import { DRIZZLE } from '../drizzle/drizzle.module';
import { DrizzleDB } from '../drizzle/types/drizzle';

@Injectable()
export class GamesService {
  constructor(
    @Inject(DRIZZLE) private readonly db: DrizzleDB,
    private readonly activityLogService: ActivityLogService,
    private readonly mediaService: MediaService,
    private readonly usersService: UsersService,
  ) {}

  async create(
    userId: string,
    businessId: string | null,
    createGameDto: CreateGameDto,
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if a game with the same name already exists for this business
      // Using ilike for case-insensitive comparison
      const existingGame = await this.db
        .select()
        .from(games)
        .where(
          and(
            eq(games.businessId, businessId),
            ilike(games.name, createGameDto.name),
            isNull(games.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (existingGame) {
        throw new ConflictException(
          `A game with the name "${createGameDto.name}" already exists`,
        );
      }

      // Generate unique publicId if not provided
      let publicId = createGameDto.publicId;
      if (!publicId) {
        publicId = await this.generateUniquePublicId(
          businessId,
          createGameDto.game,
        );
      } else {
        // Check if the provided publicId already exists for this business
        const existingPublicId = await this.db
          .select()
          .from(games)
          .where(
            and(
              eq(games.businessId, businessId),
              eq(games.publicId, publicId),
              isNull(games.deletedAt),
            ),
          )
          .then((results) => results[0]);

        if (existingPublicId) {
          throw new ConflictException(
            `A game with the public ID "${publicId}" already exists`,
          );
        }
      }

      let ogImageId: string | undefined;

      // Handle ogImage upload if provided
      if (ogImageFile) {
        const ogImageResult = await this.mediaService.uploadMediaWithReference(
          ogImageFile,
          MediaReferenceType.GAMES,
          userId,
          businessId,
          null, // referenceId - will be set after game creation
        );
        ogImageId = ogImageResult.id;
      }

      // Create the game
      const newGame = await this.db
        .insert(games)
        .values({
          businessId,
          name: createGameDto.name,
          description: createGameDto.description,
          publicId: publicId,
          game: createGameDto.game,
          configuration: createGameDto.configuration,
          promoCode: createGameDto.promoCode,
          ogImage: ogImageId,
          status: createGameDto.status || GameStatus.ACTIVE,
          createdBy: userId,
          updatedBy: userId,
        })
        .returning({ id: games.id })
        .then((results) => results[0]);

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.CREATE,
        `Created game: ${createGameDto.name}`,
        { id: newGame.id, type: 'game' },
        { id: userId, type: 'user' },
      );

      return { id: newGame.id };
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      throw new BadRequestException('Failed to create game');
    }
  }

  async bulkCreate(
    userId: string,
    businessId: string | null,
    createGameDtos: CreateGameDto[],
  ): Promise<BulkGameIdsResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const createdIds: string[] = [];
    const errors: string[] = [];

    for (const createGameDto of createGameDtos) {
      try {
        const result = await this.create(userId, businessId, createGameDto);
        createdIds.push(result.id);
      } catch (error) {
        errors.push(
          `Failed to create game "${createGameDto.name}": ${error.message}`,
        );
      }
    }

    if (errors.length > 0 && createdIds.length === 0) {
      throw new BadRequestException(
        `All games failed to create: ${errors.join(', ')}`,
      );
    }

    return { ids: createdIds };
  }

  async findAll(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
  ): Promise<{
    data: GameDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build where conditions
    const whereConditions = [
      isNull(games.deletedAt),
      eq(games.status, GameStatus.ACTIVE),
      eq(games.businessId, businessId),
    ];

    // Add date range filtering if provided
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(games.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(games.createdAt, toDate));
      }
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(games)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    const total = totalResult.count;
    const totalPages = Math.ceil(total / limit);

    // Get games with pagination
    const gameResults = await this.db
      .select()
      .from(games)
      .leftJoin(media, eq(games.ogImage, media.id))
      .where(and(...whereConditions))
      .orderBy(desc(games.createdAt), desc(games.id))
      .limit(limit)
      .offset(offset);

    // Map to DTOs
    const gameDtos = await Promise.all(
      gameResults.map((result) => this.mapToGameDto(result.games)),
    );

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.VIEW,
      'Viewed games list',
      { id: businessId, type: 'business' },
      { id: userId, type: 'user' },
    );

    return {
      data: gameDtos,
      meta: { total, page, totalPages },
    };
  }

  async findAllOptimized(
    userId: string,
    businessId: string | null,
    page = 1,
    limit = 10,
    from?: string,
    to?: string,
    name?: string,
    publicId?: string,
    game?: string,
    status?: string,
    filters?: string,
    joinOperator?: 'and' | 'or',
    sort?: string,
  ): Promise<{
    data: GameListDto[];
    meta: { total: number; page: number; totalPages: number };
  }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const offset = (page - 1) * limit;

    // Build base where conditions
    const whereConditions = [
      isNull(games.deletedAt),
      eq(games.businessId, businessId),
    ];

    // Add basic filters
    if (name) {
      whereConditions.push(ilike(games.name, `%${name}%`));
    }

    if (publicId) {
      whereConditions.push(ilike(games.publicId, `%${publicId}%`));
    }

    if (game) {
      whereConditions.push(eq(games.game, game as any));
    }

    if (status) {
      whereConditions.push(eq(games.status, status as any));
    }

    // Add date range filtering
    if (from) {
      const fromDate = new Date(from);
      if (!isNaN(fromDate.getTime())) {
        whereConditions.push(gte(games.createdAt, fromDate));
      }
    }

    if (to) {
      const toDate = new Date(to);
      if (!isNaN(toDate.getTime())) {
        whereConditions.push(lte(games.createdAt, toDate));
      }
    }

    // Handle advanced filters
    if (filters) {
      try {
        const parsedFilters = JSON.parse(filters);
        const filterConditions = [];

        for (const filter of parsedFilters) {
          const { field, operator, value } = filter;

          switch (field) {
            case 'name':
              if (operator === 'contains') {
                filterConditions.push(ilike(games.name, `%${value}%`));
              } else if (operator === 'equals') {
                filterConditions.push(eq(games.name, value));
              }
              break;
            case 'publicId':
              if (operator === 'contains') {
                filterConditions.push(ilike(games.publicId, `%${publicId}%`));
              } else if (operator === 'equals') {
                filterConditions.push(eq(games.publicId, value));
              }
              break;
            case 'game':
              filterConditions.push(eq(games.game, value));
              break;
            case 'status':
              filterConditions.push(eq(games.status, value));
              break;
          }
        }

        if (filterConditions.length > 0) {
          if (joinOperator === 'or') {
            whereConditions.push(or(...filterConditions));
          } else {
            whereConditions.push(...filterConditions);
          }
        }
      } catch {
        // Invalid JSON filters, ignore
      }
    }

    // Build order by clause
    let orderBy;
    if (sort) {
      const [field, direction] = sort.split(':');
      const isDesc = direction === 'desc';

      switch (field) {
        case 'name':
          orderBy = isDesc ? desc(games.name) : asc(games.name);
          break;
        case 'publicId':
          orderBy = isDesc ? desc(games.publicId) : asc(games.publicId);
          break;
        case 'game':
          orderBy = isDesc ? desc(games.game) : asc(games.game);
          break;
        case 'status':
          orderBy = isDesc ? desc(games.status) : asc(games.status);
          break;

        case 'createdAt':
        default:
          orderBy = isDesc ? desc(games.createdAt) : asc(games.createdAt);
          break;
      }
    } else {
      orderBy = desc(games.createdAt);
    }

    // Get total count
    const totalResult = await this.db
      .select({ count: count() })
      .from(games)
      .where(and(...whereConditions))
      .then((results) => results[0]);

    const total = totalResult.count;
    const totalPages = Math.ceil(total / limit);

    // Get games with pagination - optimized query excluding unnecessary fields
    const gameResults = await this.db
      .select({
        id: games.id,
        businessId: games.businessId,
        name: games.name,
        description: games.description,
        publicId: games.publicId,
        game: games.game,
        promoCode: games.promoCode,
        status: games.status,
        createdBy: games.createdBy,
        createdAt: games.createdAt,
        ogImage: media.id,
      })
      .from(games)
      .leftJoin(media, eq(games.ogImage, media.id))
      .where(and(...whereConditions))
      .orderBy(orderBy, desc(games.id))
      .limit(limit)
      .offset(offset);

    // Map to DTOs
    const gameDtos = await Promise.all(
      gameResults.map((result) => this.mapToGameListDto(result)),
    );

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.VIEW,
      'Viewed games list (optimized)',
      { id: businessId, type: 'business' },
      { id: userId, type: 'user' },
    );

    return {
      data: gameDtos,
      meta: { total, page, totalPages },
    };
  }

  async findAllSlim(
    userId: string,
    businessId: string | null,
  ): Promise<GameSlimDto[]> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    const gameResults = await this.db
      .select({
        id: games.id,
        name: games.name,
        publicId: games.publicId,
        game: games.game,
        status: games.status,
      })
      .from(games)
      .where(
        and(
          isNull(games.deletedAt),
          eq(games.status, GameStatus.ACTIVE),
          eq(games.businessId, businessId),
        ),
      )
      .orderBy(asc(games.name), asc(games.id));

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.VIEW,
      'Viewed games list (slim)',
      { id: businessId, type: 'business' },
      { id: userId, type: 'user' },
    );

    return gameResults.map((game) => ({
      id: game.id.toString(),
      name: game.name,
      publicId: game.publicId,
      game: game.game,
      status: game.status,
    }));
  }

  async findOne(userId: string, id: string): Promise<GameDto> {
    const gameResult = await this.db
      .select()
      .from(games)
      .leftJoin(media, eq(games.ogImage, media.id))
      .where(and(eq(games.id, id), isNull(games.deletedAt)))
      .then((results) => results[0]);

    if (!gameResult) {
      throw new NotFoundException('Game not found');
    }

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.VIEW,
      `Viewed game: ${gameResult.games.name}`,
      { id: gameResult.games.id, type: 'game' },
      { id: userId, type: 'user' },
    );

    return this.mapToGameDto(gameResult.games);
  }

  async update(
    userId: string,
    businessId: string | null,
    id: string,
    updateGameDto: UpdateGameDto,
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    try {
      if (!businessId) {
        throw new UnauthorizedException(
          'No active business found for this user',
        );
      }

      // Check if the game exists and belongs to the business
      const existingGame = await this.db
        .select()
        .from(games)
        .where(
          and(
            eq(games.id, id),
            eq(games.businessId, businessId),
            isNull(games.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existingGame) {
        throw new NotFoundException('Game not found');
      }

      // Check for name conflicts if name is being updated
      if (updateGameDto.name && updateGameDto.name !== existingGame.name) {
        const nameConflict = await this.db
          .select()
          .from(games)
          .where(
            and(
              eq(games.businessId, businessId),
              ilike(games.name, updateGameDto.name),
              isNull(games.deletedAt),
              // Exclude the current game
              ne(games.id, id),
            ),
          )
          .then((results) => results[0]);

        if (nameConflict) {
          throw new ConflictException(
            `A game with the name "${updateGameDto.name}" already exists`,
          );
        }
      }

      // Check for publicId conflicts if publicId is being updated
      if (
        updateGameDto.publicId &&
        updateGameDto.publicId !== existingGame.publicId
      ) {
        const publicIdConflict = await this.db
          .select()
          .from(games)
          .where(
            and(
              eq(games.businessId, businessId),
              eq(games.publicId, updateGameDto.publicId),
              isNull(games.deletedAt),
              // Exclude the current game
              ne(games.id, id),
            ),
          )
          .then((results) => results[0]);

        if (publicIdConflict) {
          throw new ConflictException(
            `A game with the public ID "${updateGameDto.publicId}" already exists`,
          );
        }
      }

      let ogImageId: string | undefined = existingGame.ogImage;

      // Handle ogImage upload if provided
      if (ogImageFile) {
        const ogImageResult = await this.mediaService.uploadMediaWithReference(
          ogImageFile,
          MediaReferenceType.GAMES,
          userId,
          businessId,
          null, // referenceId - will be set after game creation
        );
        ogImageId = ogImageResult.id;
      }

      // Prepare update data
      const updateData: any = {
        updatedBy: userId,
        updatedAt: new Date(),
      };

      if (updateGameDto.name !== undefined)
        updateData.name = updateGameDto.name;
      if (updateGameDto.description !== undefined)
        updateData.description = updateGameDto.description;
      if (updateGameDto.publicId !== undefined)
        updateData.publicId = updateGameDto.publicId;
      if (updateGameDto.game !== undefined)
        updateData.game = updateGameDto.game;
      if (updateGameDto.purchasedAt !== undefined)
        updateData.purchasedAt = new Date(updateGameDto.purchasedAt);
      if (updateGameDto.configuration !== undefined)
        updateData.configuration = updateGameDto.configuration;
      if (updateGameDto.promoCode !== undefined)
        updateData.promoCode = updateGameDto.promoCode;
      if (updateGameDto.status !== undefined)
        updateData.status = updateGameDto.status;
      if (ogImageId !== existingGame.ogImage) updateData.ogImage = ogImageId;

      // Update the game
      await this.db.update(games).set(updateData).where(eq(games.id, id));

      // Log the activity
      await this.activityLogService.log(
        ActivityLogName.UPDATE,
        `Updated game: ${updateGameDto.name || existingGame.name}`,
        { id, type: 'game' },
        { id: userId, type: 'user' },
      );

      return { id };
    } catch (error) {
      if (
        error instanceof ConflictException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      throw new BadRequestException('Failed to update game');
    }
  }

  async remove(
    userId: string,
    businessId: string | null,
    id: string,
  ): Promise<DeleteGameResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if the game exists and belongs to the business
    const existingGame = await this.db
      .select()
      .from(games)
      .where(
        and(
          eq(games.id, id),
          eq(games.businessId, businessId),
          isNull(games.deletedAt),
        ),
      )
      .then((results) => results[0]);

    if (!existingGame) {
      throw new NotFoundException('Game not found');
    }

    // Soft delete the game
    await this.db
      .update(games)
      .set({
        deletedAt: new Date(),
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(eq(games.id, id));

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.DELETE,
      `Deleted game: ${existingGame.name}`,
      { id, type: 'game' },
      { id: userId, type: 'user' },
    );

    return {
      message: 'Game deleted successfully',
      id,
    };
  }

  async bulkDelete(
    userId: string,
    businessId: string | null,
    gameIds: string[],
  ): Promise<BulkDeleteGameResponseDto> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    if (!gameIds || gameIds.length === 0) {
      throw new BadRequestException('No game IDs provided');
    }

    // Check which games exist and belong to the business
    const existingGames = await this.db
      .select({ id: games.id, name: games.name })
      .from(games)
      .where(
        and(
          inArray(games.id, gameIds),
          eq(games.businessId, businessId),
          isNull(games.deletedAt),
        ),
      );

    if (existingGames.length === 0) {
      throw new NotFoundException('No games found to delete');
    }

    const existingGameIds = existingGames.map((game) => game.id);

    // Soft delete the games
    await this.db
      .update(games)
      .set({
        deletedAt: new Date(),
        updatedBy: userId,
        updatedAt: new Date(),
      })
      .where(inArray(games.id, existingGameIds));

    // Log the activity
    await this.activityLogService.log(
      ActivityLogName.DELETE,
      `Bulk deleted ${existingGames.length} games`,
      { id: businessId, type: 'business' },
      { id: userId, type: 'user' },
    );

    return {
      deletedCount: existingGames.length,
      deletedIds: existingGameIds,
    };
  }

  async checkNameAvailability(
    businessId: string | null,
    name: string,
  ): Promise<{ available: boolean }> {
    if (!businessId) {
      throw new UnauthorizedException('No active business found for this user');
    }

    // Check if a game with the same name already exists for this business
    // Using ilike for case-insensitive comparison
    const existingGame = await this.db
      .select()
      .from(games)
      .where(
        and(
          eq(games.businessId, businessId),
          ilike(games.name, name),
          isNull(games.deletedAt),
        ),
      )
      .then((results) => results[0]);

    return { available: !existingGame };
  }

  async createAndReturnId(
    userId: string,
    businessId: string | null,
    createGameDto: CreateGameDto,
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    const game = await this.create(
      userId,
      businessId,
      createGameDto,
      ogImageFile,
    );
    return { id: game.id };
  }

  async updateAndReturnId(
    userId: string,
    businessId: string | null,
    id: string,
    updateGameDto: UpdateGameDto,
    ogImageFile?: Express.Multer.File,
  ): Promise<{ id: string }> {
    const game = await this.update(
      userId,
      businessId,
      id,
      updateGameDto,
      ogImageFile,
    );
    return { id: game.id };
  }

  async bulkCreateAndReturnIds(
    userId: string,
    businessId: string | null,
    createGameDtos: CreateGameDto[],
  ): Promise<BulkGameIdsResponseDto> {
    return this.bulkCreate(userId, businessId, createGameDtos);
  }

  // Helper methods for mapping data to DTOs
  private async mapToGameDto(
    game: typeof games.$inferSelect,
  ): Promise<GameDto> {
    // Get user names for createdBy and updatedBy
    const createdByName = await this.usersService.getUserName(
      game.createdBy.toString(),
    );
    let updatedByName: string | undefined;
    if (game.updatedBy) {
      updatedByName = await this.usersService.getUserName(
        game.updatedBy.toString(),
      );
    }

    // Get signed URL for ogImage if it exists
    let ogImageUrl: string | undefined;
    if (game.ogImage) {
      try {
        ogImageUrl = await this.mediaService.generateSignedUrlForMedia(
          game.ogImage,
          game.businessId,
          MediaReferenceType.GAMES,
          60, // 60 minutes expiration
        );
      } catch (error) {
        console.warn(
          `Failed to generate signed URL for game ${game.id} OG image:`,
          error.message,
        );
      }
    }

    return {
      id: game.id.toString(),
      businessId: game.businessId.toString(),
      name: game.name,
      description: game.description,
      publicId: game.publicId,
      game: game.game,
      configuration: game.configuration,
      promoCode: game.promoCode?.toString(),
      ogImage: ogImageUrl,
      status: game.status,
      createdBy: createdByName,
      updatedBy: updatedByName,
      createdAt: game.createdAt.toISOString(),
      updatedAt: game.updatedAt?.toISOString(),
    };
  }

  private async mapToGameListDto(result: any): Promise<GameListDto> {
    // Get user name for createdBy
    const createdByName = await this.usersService.getUserName(
      result.createdBy.toString(),
    );

    // Get signed URL for ogImage if it exists
    let ogImageUrl: string | undefined;
    if (result.ogImage) {
      try {
        ogImageUrl = await this.mediaService.generateSignedUrlForMedia(
          result.ogImage,
          result.businessId,
          MediaReferenceType.GAMES,
          60, // 60 minutes expiration
        );
      } catch (error) {
        console.warn(
          `Failed to generate signed URL for game ${result.id} OG image:`,
          error.message,
        );
      }
    }

    return {
      id: result.id.toString(),
      name: result.name,
      description: result.description,
      publicId: result.publicId,
      game: result.game,
      purchasedAt: result.purchasedAt.toISOString(),
      promoCode: result.promoCode?.toString(),
      ogImage: ogImageUrl,
      status: result.status,
      createdBy: createdByName,
      createdAt: result.createdAt.toISOString(),
    };
  }

  /**
   * Generate a unique publicId for a game within a business
   */
  private async generateUniquePublicId(
    businessId: string,
    gameType: string,
  ): Promise<string> {
    const gamePrefix = gameType.replace('_', '-').toLowerCase();
    let attempt = 0;
    const maxAttempts = 10;

    while (attempt < maxAttempts) {
      const timestamp = Date.now();
      const randomSuffix = Math.random().toString(36).substring(2, 8);
      const publicId = `${gamePrefix}-${timestamp}-${randomSuffix}`;

      // Check if this publicId already exists for this business
      const existing = await this.db
        .select()
        .from(games)
        .where(
          and(
            eq(games.businessId, businessId),
            eq(games.publicId, publicId),
            isNull(games.deletedAt),
          ),
        )
        .then((results) => results[0]);

      if (!existing) {
        return publicId;
      }

      attempt++;
      // Add a small delay to ensure different timestamps
      await new Promise((resolve) => setTimeout(resolve, 1));
    }

    throw new Error(
      'Failed to generate unique publicId after maximum attempts',
    );
  }
}
