import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsArray } from 'class-validator';
import { Transform } from 'class-transformer';
import { CreateAllowanceTypeDto } from './create-allowance-type.dto';

export class BulkCreateAllowanceTypeDto {
  @ApiProperty({
    description: 'JSON string containing array of allowance type objects',
    example:
      '[{"allowanceName":"Housing Allowance","allowanceCode":"HOUSING","calculationMethod":"FIXED","isTaxable":true,"isActive":true},{"allowanceName":"Transport Allowance","allowanceCode":"TRANSPORT","calculationMethod":"PERCENTAGE","isTaxable":false,"isActive":true}]',
  })
  @IsNotEmpty({ message: 'Allowance types are required' })
  @Transform(({ value }) => {
    try {
      return typeof value === 'string' ? JSON.parse(value) : value;
    } catch {
      return value;
    }
  })
  @IsArray({ message: 'Allowance types must be an array' })
  allowanceTypes: CreateAllowanceTypeDto[];
}
