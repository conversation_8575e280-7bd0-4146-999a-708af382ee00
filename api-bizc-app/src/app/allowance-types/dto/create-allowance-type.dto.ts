import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsOptional,
  IsString,
  MaxLength,
  IsBoolean,
  IsEnum,
  IsUUID,
  IsNumber,
} from 'class-validator';
import { Transform } from 'class-transformer';
import { AllowanceCalculationMethod } from '../../shared/types';

export class CreateAllowanceTypeDto {
  @ApiProperty({ description: 'Allowance name', maxLength: 191 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(191)
  allowanceName: string;

  @ApiProperty({ description: 'Allowance code', maxLength: 191 })
  @IsNotEmpty()
  @IsString()
  @MaxLength(191)
  allowanceCode: string;

  @ApiProperty({
    description: 'Calculation method for the allowance',
    enum: AllowanceCalculationMethod,
    example: AllowanceCalculationMethod.FIXED,
  })
  @IsNotEmpty()
  @IsEnum(AllowanceCalculationMethod, {
    message: 'Calculation method must be a valid allowance calculation method',
  })
  calculationMethod: AllowanceCalculationMethod;

  @ApiPropertyOptional({
    description: 'Whether this allowance is taxable',
    default: true,
  })
  @IsBoolean({ message: 'Is taxable must be a boolean' })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  isTaxable?: boolean;

  @ApiPropertyOptional({
    description: 'Tax rate ID',
  })
  @IsOptional()
  @IsUUID()
  taxRateId?: string;

  @ApiPropertyOptional({
    description: 'Whether this allowance is active',
    default: true,
  })
  @IsBoolean({ message: 'Is active must be a boolean' })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Whether this allowance is eligible for EPF/ETF',
    default: false,
  })
  @IsBoolean({ message: 'Is EPF/ETF eligible must be a boolean' })
  @IsOptional()
  @Transform(({ value }) => {
    if (typeof value === 'string') {
      return value.toLowerCase() === 'true';
    }
    return value;
  })
  isEPFETFEligible?: boolean;

  @ApiPropertyOptional({
    description: 'Amount',
  })
  @IsOptional()
  @IsNumber()
  amount?: number;

  @ApiPropertyOptional({
    description: 'Allowance type description',
  })
  @IsOptional()
  @IsString()
  description?: string;
}
