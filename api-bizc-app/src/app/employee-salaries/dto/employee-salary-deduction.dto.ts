import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsUUID,
  IsOptional,
  IsN<PERSON>ber,
  Min,
  IsString,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateEmployeeSalaryDeductionDto {
  @ApiProperty({
    description: 'Deduction type ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty({ message: 'Deduction type ID is required' })
  @IsUUID(4, { message: 'Deduction type ID must be a valid UUID' })
  deductionTypeId: string;

  @ApiProperty({
    description: 'Deduction amount',
    example: 1000.00,
    type: 'number',
  })
  @IsNotEmpty({ message: 'Amount is required' })
  @Transform(({ value }) => parseFloat(value))
  @IsNumber({}, { message: 'Amount must be a number' })
  @Min(0, { message: 'Amount must be greater than or equal to 0' })
  amount: number;

  @ApiPropertyOptional({
    description: 'Notes about the deduction',
    example: 'Monthly insurance deduction',
  })
  @IsOptional()
  @IsString({ message: 'Notes must be a string' })
  notes?: string;
}

export class UpdateEmployeeSalaryDeductionDto {
  @ApiPropertyOptional({
    description: 'Employee salary deduction ID (for updates)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'ID must be a valid UUID' })
  id?: string;

  @ApiProperty({
    description: 'Deduction type ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty({ message: 'Deduction type ID is required' })
  @IsUUID(4, { message: 'Deduction type ID must be a valid UUID' })
  deductionTypeId: string;

  @ApiProperty({
    description: 'Deduction amount',
    example: 1000.00,
    type: 'number',
  })
  @IsNotEmpty({ message: 'Amount is required' })
  @Transform(({ value }) => parseFloat(value))
  @IsNumber({}, { message: 'Amount must be a number' })
  @Min(0, { message: 'Amount must be greater than or equal to 0' })
  amount: number;

  @ApiPropertyOptional({
    description: 'Notes about the deduction',
    example: 'Monthly insurance deduction',
  })
  @IsOptional()
  @IsString({ message: 'Notes must be a string' })
  notes?: string;
}

export class EmployeeSalaryDeductionDto {
  @ApiProperty({
    description: 'Employee salary deduction ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @ApiProperty({
    description: 'Deduction type ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  deductionTypeId: string;

  @ApiProperty({
    description: 'Deduction type name',
    example: 'Insurance Deduction',
  })
  deductionTypeName: string;

  @ApiProperty({
    description: 'Deduction type code',
    example: 'INSURANCE',
  })
  deductionTypeCode: string;

  @ApiProperty({
    description: 'Deduction amount',
    example: 1000.00,
    type: 'number',
  })
  amount: number;

  @ApiPropertyOptional({
    description: 'Notes about the deduction',
    example: 'Monthly insurance deduction',
    nullable: true,
  })
  notes?: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt: Date;
}
