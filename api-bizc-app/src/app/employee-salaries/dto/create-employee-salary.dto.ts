import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsString,
  IsUUID,
  IsOptional,
  IsEnum,
  IsDecimal,
  IsNumber,
  Min,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { EmployeeSalaryStatus } from '../../drizzle/schema/employee-salary.schema';
import { CreateEmployeeSalaryAllowanceDto } from './employee-salary-allowance.dto';
import { CreateEmployeeSalaryDeductionDto } from './employee-salary-deduction.dto';

export class CreateEmployeeSalaryDto {
  @ApiProperty({
    description: 'Employee ID (reference to staff member)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty({ message: 'Employee ID is required' })
  @IsUUID(4, { message: 'Employee ID must be a valid UUID' })
  employeeId: string;

  @ApiProperty({
    description: 'Basic salary amount',
    example: 50000.0,
    type: 'number',
  })
  @IsNotEmpty({ message: 'Basic salary is required' })
  @Transform(({ value }) => parseFloat(value))
  @IsNumber({}, { message: 'Basic salary must be a number' })
  @Min(0, { message: 'Basic salary must be greater than or equal to 0' })
  basicSalary: number;

  @ApiProperty({
    description: 'Bank account ID for salary payments',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty({ message: 'Bank account ID is required' })
  @IsUUID(4, { message: 'Bank account ID must be a valid UUID' })
  bankAccountId: string;

  @ApiPropertyOptional({
    description: 'Employee salary status',
    enum: EmployeeSalaryStatus,
    enumName: 'EmployeeSalaryStatus',
    example: EmployeeSalaryStatus.ACTIVE,
    default: EmployeeSalaryStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EmployeeSalaryStatus, {
    message: 'Status must be a valid employee salary status',
  })
  status?: EmployeeSalaryStatus;

  @ApiPropertyOptional({
    description: 'Array of allowances to create with the salary',
    type: [CreateEmployeeSalaryAllowanceDto],
    example: [
      {
        allowanceTypeId: '550e8400-e29b-41d4-a716-************',
        amount: 5000.0,
        notes: 'Monthly transport allowance',
      },
    ],
  })
  @IsOptional()
  @IsArray({ message: 'Allowances must be an array' })
  @ValidateNested({ each: true })
  @Type(() => CreateEmployeeSalaryAllowanceDto)
  allowances?: CreateEmployeeSalaryAllowanceDto[];

  @ApiPropertyOptional({
    description: 'Array of deductions to create with the salary',
    type: [CreateEmployeeSalaryDeductionDto],
    example: [
      {
        deductionTypeId: '550e8400-e29b-41d4-a716-************',
        amount: 1000.0,
        notes: 'Monthly insurance deduction',
      },
    ],
  })
  @IsOptional()
  @IsArray({ message: 'Deductions must be an array' })
  @ValidateNested({ each: true })
  @Type(() => CreateEmployeeSalaryDeductionDto)
  deductions?: CreateEmployeeSalaryDeductionDto[];
}
