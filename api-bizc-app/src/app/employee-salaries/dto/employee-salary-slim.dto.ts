import { ApiProperty } from '@nestjs/swagger';
import { EmployeeSalaryStatus } from '../../drizzle/schema/employee-salary.schema';

export class EmployeeSalarySlimDto {
  @ApiProperty({
    description: 'Unique identifier for the employee salary',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Employee ID (reference to staff member)',
    example: '550e8400-e29b-41d4-a716-446655440002',
  })
  employeeId: string;

  @ApiProperty({
    description: 'Employee display name',
    example: '<PERSON>',
  })
  employeeDisplayName: string;

  @ApiProperty({
    description: 'Basic salary amount',
    example: 50000.0,
    type: 'number',
  })
  basicSalary: number;

  @ApiProperty({
    description: 'Employee salary status',
    enum: EmployeeSalaryStatus,
    enumName: 'EmployeeSalaryStatus',
    example: EmployeeSalaryStatus.ACTIVE,
  })
  status: EmployeeSalaryStatus;
}
