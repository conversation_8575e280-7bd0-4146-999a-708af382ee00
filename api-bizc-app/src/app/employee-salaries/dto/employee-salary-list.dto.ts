import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { EmployeeSalaryStatus } from '../../drizzle/schema/employee-salary.schema';

export class EmployeeSalaryListDto {
  @ApiProperty({
    description: 'Unique identifier for the employee salary',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  id: string;

  @ApiProperty({
    description: 'Business ID the employee salary belongs to',
    example: '550e8400-e29b-41d4-a716-446655440001',
  })
  businessId: string;

  @ApiProperty({
    description: 'Employee ID (reference to staff member)',
    example: '550e8400-e29b-41d4-a716-446655440002',
  })
  employeeId: string;

  @ApiProperty({
    description: 'Employee display name',
    example: '<PERSON>',
  })
  employeeDisplayName: string;

  @ApiPropertyOptional({
    description: 'Employee email',
    example: '<EMAIL>',
    nullable: true,
  })
  employeeEmail?: string;

  @ApiPropertyOptional({
    description: 'Employee phone number',
    example: '+**********',
    nullable: true,
  })
  employeePhone?: string;

  @ApiProperty({
    description: 'Basic salary amount',
    example: 50000.0,
    type: 'number',
  })
  basicSalary: number;

  @ApiProperty({
    description: 'Bank account ID for salary payments',
    example: '550e8400-e29b-41d4-a716-************',
  })
  bankAccountId: string;

  @ApiProperty({
    description: 'Bank account holder name',
    example: 'John Doe',
  })
  bankAccountHolderName: string;

  @ApiProperty({
    description: 'Bank name',
    example: 'ABC Bank',
  })
  bankName: string;

  @ApiProperty({
    description: 'Bank account number',
    example: '**********',
  })
  bankAccountNumber: string;

  @ApiProperty({
    description: 'Employee salary status',
    enum: EmployeeSalaryStatus,
    enumName: 'EmployeeSalaryStatus',
    example: EmployeeSalaryStatus.ACTIVE,
  })
  status: EmployeeSalaryStatus;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt: Date;

  @ApiProperty({
    description: 'Total allowance amount',
    example: 7000.0,
    type: 'number',
  })
  totalAllowanceAmount: number;

  @ApiProperty({
    description: 'Total deduction amount',
    example: 1500.0,
    type: 'number',
  })
  totalDeductionAmount: number;
}
