import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsNotEmpty,
  IsUUID,
  IsOptional,
  IsN<PERSON>ber,
  Min,
  IsString,
} from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateEmployeeSalaryAllowanceDto {
  @ApiProperty({
    description: 'Allowance type ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty({ message: 'Allowance type ID is required' })
  @IsUUID(4, { message: 'Allowance type ID must be a valid UUID' })
  allowanceTypeId: string;

  @ApiProperty({
    description: 'Allowance amount',
    example: 5000.00,
    type: 'number',
  })
  @IsNotEmpty({ message: 'Amount is required' })
  @Transform(({ value }) => parseFloat(value))
  @IsNumber({}, { message: 'Amount must be a number' })
  @Min(0, { message: 'Amount must be greater than or equal to 0' })
  amount: number;

  @ApiPropertyOptional({
    description: 'Notes about the allowance',
    example: 'Monthly transport allowance',
  })
  @IsOptional()
  @IsString({ message: 'Notes must be a string' })
  notes?: string;
}

export class UpdateEmployeeSalaryAllowanceDto {
  @ApiPropertyOptional({
    description: 'Employee salary allowance ID (for updates)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'ID must be a valid UUID' })
  id?: string;

  @ApiProperty({
    description: 'Allowance type ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsNotEmpty({ message: 'Allowance type ID is required' })
  @IsUUID(4, { message: 'Allowance type ID must be a valid UUID' })
  allowanceTypeId: string;

  @ApiProperty({
    description: 'Allowance amount',
    example: 5000.00,
    type: 'number',
  })
  @IsNotEmpty({ message: 'Amount is required' })
  @Transform(({ value }) => parseFloat(value))
  @IsNumber({}, { message: 'Amount must be a number' })
  @Min(0, { message: 'Amount must be greater than or equal to 0' })
  amount: number;

  @ApiPropertyOptional({
    description: 'Notes about the allowance',
    example: 'Monthly transport allowance',
  })
  @IsOptional()
  @IsString({ message: 'Notes must be a string' })
  notes?: string;
}

export class EmployeeSalaryAllowanceDto {
  @ApiProperty({
    description: 'Employee salary allowance ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  id: string;

  @ApiProperty({
    description: 'Allowance type ID',
    example: '550e8400-e29b-41d4-a716-************',
  })
  allowanceTypeId: string;

  @ApiProperty({
    description: 'Allowance type name',
    example: 'Transport Allowance',
  })
  allowanceTypeName: string;

  @ApiProperty({
    description: 'Allowance type code',
    example: 'TRANSPORT',
  })
  allowanceTypeCode: string;

  @ApiProperty({
    description: 'Allowance amount',
    example: 5000.00,
    type: 'number',
  })
  amount: number;

  @ApiPropertyOptional({
    description: 'Notes about the allowance',
    example: 'Monthly transport allowance',
    nullable: true,
  })
  notes?: string;

  @ApiProperty({
    description: 'Creation timestamp',
    example: '2024-01-15T10:30:00Z',
  })
  createdAt: Date;

  @ApiProperty({
    description: 'Last update timestamp',
    example: '2024-01-15T10:30:00Z',
  })
  updatedAt: Date;
}
