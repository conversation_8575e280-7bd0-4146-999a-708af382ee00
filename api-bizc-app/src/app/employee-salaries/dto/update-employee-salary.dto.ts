import { ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsOptional,
  IsString,
  IsUUID,
  IsEnum,
  IsDecimal,
  IsNumber,
  Min,
  IsArray,
  ValidateNested,
} from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { EmployeeSalaryStatus } from '../../drizzle/schema/employee-salary.schema';
import { UpdateEmployeeSalaryAllowanceDto } from './employee-salary-allowance.dto';
import { UpdateEmployeeSalaryDeductionDto } from './employee-salary-deduction.dto';

export class UpdateEmployeeSalaryDto {
  @ApiPropertyOptional({
    description: 'Employee ID (reference to staff member)',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Employee ID must be a valid UUID' })
  employeeId?: string;

  @ApiPropertyOptional({
    description: 'Basic salary amount',
    example: 55000.0,
    type: 'number',
  })
  @IsOptional()
  @Transform(({ value }) =>
    value !== undefined ? parseFloat(value) : undefined,
  )
  @IsNumber({}, { message: 'Basic salary must be a number' })
  @Min(0, { message: 'Basic salary must be greater than or equal to 0' })
  basicSalary?: number;

  @ApiPropertyOptional({
    description: 'Bank account ID for salary payments',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Bank account ID must be a valid UUID' })
  bankAccountId?: string;

  @ApiPropertyOptional({
    description: 'Employee salary status',
    enum: EmployeeSalaryStatus,
    enumName: 'EmployeeSalaryStatus',
    example: EmployeeSalaryStatus.ACTIVE,
  })
  @IsOptional()
  @IsEnum(EmployeeSalaryStatus, {
    message: 'Status must be a valid employee salary status',
  })
  status?: EmployeeSalaryStatus;

  @ApiPropertyOptional({
    description:
      'Array of allowances to update/create with the salary. Include id for updates, omit for new allowances.',
    type: [UpdateEmployeeSalaryAllowanceDto],
    example: [
      {
        id: '550e8400-e29b-41d4-a716-************',
        allowanceTypeId: '550e8400-e29b-41d4-a716-************',
        amount: 5500.0,
        notes: 'Updated transport allowance',
      },
      {
        allowanceTypeId: '550e8400-e29b-41d4-a716-************',
        amount: 2000.0,
        notes: 'New meal allowance',
      },
    ],
  })
  @IsOptional()
  @IsArray({ message: 'Allowances must be an array' })
  @ValidateNested({ each: true })
  @Type(() => UpdateEmployeeSalaryAllowanceDto)
  allowances?: UpdateEmployeeSalaryAllowanceDto[];

  @ApiPropertyOptional({
    description:
      'Array of deductions to update/create with the salary. Include id for updates, omit for new deductions.',
    type: [UpdateEmployeeSalaryDeductionDto],
    example: [
      {
        id: '550e8400-e29b-41d4-a716-446655440003',
        deductionTypeId: '550e8400-e29b-41d4-a716-446655440004',
        amount: 1200.0,
        notes: 'Updated insurance deduction',
      },
      {
        deductionTypeId: '550e8400-e29b-41d4-a716-446655440005',
        amount: 500.0,
        notes: 'New loan deduction',
      },
    ],
  })
  @IsOptional()
  @IsArray({ message: 'Deductions must be an array' })
  @ValidateNested({ each: true })
  @Type(() => UpdateEmployeeSalaryDeductionDto)
  deductions?: UpdateEmployeeSalaryDeductionDto[];
}
