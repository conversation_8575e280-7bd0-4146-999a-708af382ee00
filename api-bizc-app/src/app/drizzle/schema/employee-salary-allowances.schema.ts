import {
  timestamp,
  pgTable,
  text,
  uuid,
  decimal,
  index,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { isNull } from 'drizzle-orm';

import { business } from './business.schema';
import { users } from './users.schema';
import { employeeSalaries } from './employee-salary.schema';
import { allowanceTypes } from './allowance-types.schema';

/**
 * Employee Salary Allowances Table
 * Links employee salaries with their allowances and stores specific allowance amounts
 */
export const employeeSalaryAllowances = pgTable(
  'employee_salary_allowances',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    employeeSalaryId: uuid('employee_salary_id')
      .notNull()
      .references(() => employeeSalaries.id),
    allowanceTypeId: uuid('allowance_type_id')
      .notNull()
      .references(() => allowanceTypes.id),
    amount: decimal('amount', { precision: 12, scale: 2 }),
    notes: text('notes'),
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    deletedBy: uuid('deleted_by').references(() => users.id),
    deletedAt: timestamp('deleted_at'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    businessIdIndex: index('employee_salary_allowances_business_id_index').on(
      t.businessId,
    ),
    employeeSalaryIdIndex: index(
      'employee_salary_allowances_employee_salary_id_index',
    ).on(t.employeeSalaryId),
    allowanceTypeIdIndex: index(
      'employee_salary_allowances_allowance_type_id_index',
    ).on(t.allowanceTypeId),
    uniqueEmployeeSalaryAllowanceActive: uniqueIndex(
      'employee_salary_allowances_employee_salary_allowance_active_unique',
    )
      .on(t.employeeSalaryId, t.allowanceTypeId)
      .where(isNull(t.deletedAt)),
  }),
);
