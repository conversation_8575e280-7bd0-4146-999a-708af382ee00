import {
  timestamp,
  pgTable,
  text,
  uuid,
  decimal,
  index,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { isNull } from 'drizzle-orm';

import { business } from './business.schema';
import { users } from './users.schema';
import { employeeSalaries } from './employee-salary.schema';
import { deductionTypes } from './deduction-types.schema';

/**
 * Employee Salary Deductions Table
 * Links employee salaries with their deductions and stores specific deduction amounts
 */
export const employeeSalaryDeductions = pgTable(
  'employee_salary_deductions',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    employeeSalaryId: uuid('employee_salary_id')
      .notNull()
      .references(() => employeeSalaries.id),
    deductionTypeId: uuid('deduction_type_id')
      .notNull()
      .references(() => deductionTypes.id),
    amount: decimal('amount', { precision: 12, scale: 2 }),
    notes: text('notes'),
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    deletedBy: uuid('deleted_by').references(() => users.id),
    deletedAt: timestamp('deleted_at'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    businessIdIndex: index('employee_salary_deductions_business_id_index').on(
      t.businessId,
    ),
    employeeSalaryIdIndex: index(
      'employee_salary_deductions_employee_salary_id_index',
    ).on(t.employeeSalaryId),
    deductionTypeIdIndex: index(
      'employee_salary_deductions_deduction_type_id_index',
    ).on(t.deductionTypeId),
    uniqueEmployeeSalaryDeductionActive: uniqueIndex(
      'employee_salary_deductions_employee_salary_deduction_active_unique',
    )
      .on(t.employeeSalaryId, t.deductionTypeId)
      .where(isNull(t.deletedAt)),
  }),
);
