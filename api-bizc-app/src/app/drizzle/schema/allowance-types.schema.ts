import {
  timestamp,
  pgTable,
  text,
  uuid,
  boolean,
  decimal,
  pgEnum,
  index,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { isNull } from 'drizzle-orm';

import { business } from './business.schema';
import { users } from './users.schema';
import { taxes } from './taxes.schema';
import {
  AllowanceCalculationMethod,
  AllowanceStatus,
} from '../../shared/types';

export const calculationMethodEnum = pgEnum('calculation_method', [
  AllowanceCalculationMethod.FIXED,
  AllowanceCalculationMethod.PERCENTAGE,
  AllowanceCalculationMethod.FORMULA,
  AllowanceCalculationMethod.SLAB,
]);

export const allowanceStatusEnum = pgEnum('allowance_status', [
  AllowanceStatus.ACTIVE,
  AllowanceStatus.INACTIVE,
  AllowanceStatus.PENDING,
  AllowanceStatus.EXPIRED,
]);

export const allowanceTypes = pgTable(
  'allowance_types',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    allowanceName: text('allowance_name').notNull(),
    allowanceCode: text('allowance_code').notNull(),
    calculationMethod: calculationMethodEnum('calculation_method').notNull(),
    isTaxable: boolean('is_taxable').default(true).notNull(),
    taxRateId: uuid('tax_rate_id').references(() => taxes.id),
    amount: decimal('amount', { precision: 12, scale: 2 }),
    isActive: boolean('is_active').default(true).notNull(),
    isEPFETFEligible: boolean('is_epf_etf_eligible').default(false).notNull(),
    description: text('description'),
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    deletedBy: uuid('deleted_by').references(() => users.id),
    deletedAt: timestamp('deleted_at'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    businessIdIndex: index('allowance_types_business_id_index').on(
      t.businessId,
    ),
    allowanceCodeIndex: index('allowance_types_allowance_code_index').on(
      t.allowanceCode,
    ),
    isActiveIndex: index('allowance_types_is_active_index').on(t.isActive),
    uniqueBusinessAllowanceCode: uniqueIndex(
      'allowance_types_business_allowance_code_unique',
    )
      .on(t.businessId, t.allowanceCode)
      .where(isNull(t.deletedAt)),
  }),
);
