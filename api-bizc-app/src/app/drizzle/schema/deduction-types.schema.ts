import {
  timestamp,
  pgTable,
  text,
  uuid,
  boolean,
  decimal,
  pgEnum,
  index,
  uniqueIndex,
} from 'drizzle-orm/pg-core';
import { isNull } from 'drizzle-orm';

import { business } from './business.schema';
import { users } from './users.schema';
import {
  DeductionCalculationMethod,
  DeductionAppliesTo,
  DeductionStatus,
} from '../../shared/types';

export const deductionCalculationMethodEnum = pgEnum(
  'deduction_calculation_method',
  [
    DeductionCalculationMethod.FIXED,
    DeductionCalculationMethod.PERCENTAGE,
    DeductionCalculationMethod.PROGRESSIVE,
    DeductionCalculationMethod.FORMULA,
  ],
);

export const deductionAppliesEnum = pgEnum('deduction_applies', [
  DeductionAppliesTo.GROSS,
  DeductionAppliesTo.BASIC,
]);

export const deductionStatusEnum = pgEnum('deduction_status', [
  DeductionStatus.ACTIVE,
  DeductionStatus.INACTIVE,
  DeductionStatus.PENDING,
  DeductionStatus.EXPIRED,
  DeductionStatus.COMPLETED,
]);

export const deductionTypes = pgTable(
  'deduction_types',
  {
    id: uuid('id').defaultRandom().primaryKey(),
    businessId: uuid('business_id')
      .notNull()
      .references(() => business.id),
    deductionName: text('deduction_name').notNull(),
    deductionCode: text('deduction_code').notNull(),
    calculationMethod:
      deductionCalculationMethodEnum('calculation_method').notNull(),
    amount: decimal('amount', { precision: 12, scale: 2 }),
    isSystemDefined: boolean('is_system_defined').default(false).notNull(),
    isActive: boolean('is_active').default(true).notNull(),
    appliesTo: deductionAppliesEnum('applies_to')
      .default(DeductionAppliesTo.GROSS)
      .notNull(),
    description: text('description'),
    createdBy: uuid('created_by')
      .notNull()
      .references(() => users.id),
    updatedBy: uuid('updated_by').references(() => users.id),
    deletedBy: uuid('deleted_by').references(() => users.id),
    deletedAt: timestamp('deleted_at'),
    createdAt: timestamp('created_at').defaultNow().notNull(),
    updatedAt: timestamp('updated_at').defaultNow().notNull(),
  },
  (t) => ({
    businessIdIndex: index('deduction_types_business_id_index').on(
      t.businessId,
    ),
    deductionCodeIndex: index('deduction_types_deduction_code_index').on(
      t.deductionCode,
    ),
    isSystemDefinedIndex: index('deduction_types_is_system_defined_index').on(
      t.isSystemDefined,
    ),
    isActiveIndex: index('deduction_types_is_active_index').on(t.isActive),
    uniqueBusinessDeductionCode: uniqueIndex(
      'deduction_types_business_deduction_code_unique',
    )
      .on(t.businessId, t.deductionCode)
      .where(isNull(t.deletedAt)),
  }),
);
